{"name": "ai-chat-interface", "version": "1.0.0", "private": true, "description": "Modern AI Chat Interface Design System", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1"}, "devDependencies": {"tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}