# 🎨 AI Chat Interface Design System

基于现代 AI 聊天界面分析构建的设计系统，使用 React + Tailwind CSS 实现。

## 🚀 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm start

# 构建生产版本
npm run build
```

## 📁 项目结构

```
src/
├── design-system/           # 设计系统核心
│   ├── index.js            # 统一导出
│   ├── tokens/             # 设计令牌
│   │   ├── colors.js       # 色彩系统
│   │   ├── spacing.js      # 间距系统
│   │   └── shadows.js      # 阴影系统
│   └── foundations/        # 基础组件
│       ├── Button/         # 按钮组件
│       ├── Card/           # 卡片组件
│       ├── Avatar/         # 头像组件
│       └── Menu/           # 菜单组件
├── components/             # 应用组件
│   └── LanguageSwitcher.js # 语言切换器
├── mock/                   # 模拟数据
│   └── chatData.js         # 聊天数据 (包含语音工具)
├── translations.json       # 国际化翻译文件
├── LanguageContext.js      # 语言上下文
└── App.js                  # 主应用 (Fiction Generator)
```

## 🎯 设计特点

### 色彩系统
- **主色调**: 蓝色系 `#3B82F6`
- **背景色**: 淡灰色 `#F9FAFB`
- **卡片背景**: 纯白色
- **文字层次**: 深灰 → 中灰 → 浅灰

### 组件特性
- **模块化**: 原子组件 + 组合模式
- **可配置**: 通过 props 控制变体
- **响应式**: 适配多端设备
- **一致性**: 统一的设计语言

### 🎤 语音交互功能
- **Audio Chat**: 集成语音聊天工具
- **多模态交互**: 支持文本和语音输入
- **国际化**: 语音功能完全支持多语言
- **用户友好**: 一键启动语音对话

## 📖 使用示例

```jsx
import { Button, Card, Avatar } from './design-system';

function MyComponent() {
  return (
    <Card padding="lg" hoverable>
      <div className="flex items-center gap-3">
        <Avatar 
          src="/avatar.jpg" 
          name="用户名"
          size="md" 
          online
        />
        <div className="flex-1">
          <h3 className="font-semibold">标题</h3>
          <p className="text-gray-600">描述文字</p>
        </div>
        <Button variant="primary">
          操作
        </Button>
      </div>
    </Card>
  );
}
```

## 🎤 语音功能

### Audio Chat 工具
项目集成了语音聊天功能，支持多模态交互：

```jsx
// 语音工具配置
const audioTool = {
  id: 'audio-chat',
  name: 'Audio Chat',
  icon: '🎤', 
  description: 'Voice conversation'
};
```

### 语音功能特性
- **语音识别**: 将用户语音转换为文本
- **文本转语音**: AI 回复的语音播放
- **实时交互**: 流畅的语音对话体验
- **多语言支持**: 完整的国际化语音功能

### 技术实现
- **Web Speech API**: 浏览器原生语音识别
- **音频流处理**: 实时音频数据处理
- **权限管理**: 麦克风访问权限处理
- **错误处理**: 完善的异常处理机制

## 🌐 国际化支持

### 语言切换
项目支持中英文双语切换：

```jsx
import { LanguageProvider, useLanguage } from './LanguageContext';

function MyComponent() {
  const { t, currentLanguage, switchLanguage } = useLanguage();
  
  return (
    <div>
      <h1>{t('appTitle')}</h1>
      <button onClick={() => switchLanguage('en')}>
        English
      </button>
    </div>
  );
}
```

### 翻译文件结构
```json
{
  "appTitle": {
    "zh": "小说生成器",
    "en": "Fiction Generator"
  },
  "voiceFeatures": {
    "audioChat": {
      "zh": "语音聊天", 
      "en": "Audio Chat"
    }
  }
}
```

## 🔧 组件 API

### Button 组件
```jsx
<Button
  variant="primary"    // primary | secondary | ghost | danger
  size="md"           // sm | md | lg | xl | icon
  loading={false}     // 加载状态
  disabled={false}    // 禁用状态
  onClick={handleClick}
>
  按钮文字
</Button>
```

### Card 组件
```jsx
<Card
  padding="md"        // none | sm | md | lg
  shadow="sm"         // none | sm | md | lg
  hoverable={false}   // 悬停效果
  bordered={true}     // 边框
  onClick={handleClick}
>
  卡片内容
</Card>
```

### Avatar 组件
```jsx
<Avatar
  src="/avatar.jpg"   // 头像图片
  name="用户名"       // 用于生成初始字母
  size="md"          // xs | sm | md | lg | xl
  shape="circle"     // circle | rounded | square
  online={false}     // 在线状态
/>
```

### Menu 组件
```jsx
<Menu>
  <Menu.Group title="菜单标题">
    <Menu.Item 
      icon="📊"
      active={false}         // 激活状态
      badge="Beta"           // 徽章文本
      badgeColor="blue"      // 徽章颜色 blue|green|red|yellow|purple
      onClick={handleClick}
    >
      菜单项文本
    </Menu.Item>
  </Menu.Group>
</Menu>
```

## 🎨 设计令牌

### 颜色
```js
import { colors } from './design-system';

// 主色调
colors.primary[500]  // #3B82F6

// 中性色
colors.neutral[50]   // #F9FAFB (背景)
colors.neutral[900]  // #111827 (文字)
```

### 间距
```js
import { spacing } from './design-system';

// 语义化间距
spacing.xs   // 8px
spacing.sm   // 12px
spacing.md   // 16px
spacing.lg   // 24px
```

## 📚 更多文档

- [完整架构文档](./ARCHITECTURE.md)
- [组件开发指南](./ARCHITECTURE.md#开发指南)
- [设计系统原则](./ARCHITECTURE.md#设计原则)

## 🔄 版本信息

当前版本：`1.0.0`

---

这个设计系统为你提供了构建现代 AI 聊天界面所需的所有基础组件和设计规范。
