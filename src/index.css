@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  * {
    box-sizing: border-box;
  }
}

/* 组件样式 */
@layer components {
  /* 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }

  /* 消息气泡样式 */
  .message-bubble {
    @apply relative max-w-md p-3 rounded-2xl;
  }

  .message-bubble.user {
    @apply bg-primary-500 text-white ml-auto;
  }

  .message-bubble.ai {
    @apply bg-gray-100 text-gray-900;
  }

  /* 卡片悬停效果 */
  .card-hover {
    @apply transition-all duration-200 hover:shadow-hover hover:-translate-y-0.5;
  }

  /* 输入框样式 */
  .input-field {
    @apply w-full px-4 py-3 bg-white border border-gray-200 rounded-lg;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500;
    @apply placeholder:text-gray-400;
  }

  /* 按钮基础样式 */
  .btn-base {
    @apply inline-flex items-center justify-center font-medium rounded-lg;
    @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
}

/* 工具样式 */
@layer utilities {
  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 渐变背景 */
  .gradient-primary {
    background: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
  }

  /* 玻璃效果 */
  .glass-effect {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.8);
  }

  /* 动画类 */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .bounce-animation {
    animation: bounce 0.6s ease-in-out;
  }
}

/* 关键帧动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-15px);
  }
  70% {
    transform: translateY(-7px);
  }
  90% {
    transform: translateY(-3px);
  }
}

/* 响应式隐藏/显示 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}
