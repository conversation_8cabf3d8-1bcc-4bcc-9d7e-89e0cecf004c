import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Avatar, Menu } from './design-system';
import { LanguageProvider, useLanguage } from './LanguageContext';
import LanguageSwitcher from './components/LanguageSwitcher';
import { formatRelativeTimeWithTranslation } from './utils/timeUtils';

// Custom hook for localStorage state
function useLocalStorageState(key, defaultValue) {
  const [state, setState] = useState(() => {
    try {
      const saved = localStorage.getItem(key);
      return saved ? JSON.parse(saved) : defaultValue;
    } catch {
      return defaultValue;
    }
  });

  useEffect(() => {
    localStorage.setItem(key, JSON.stringify(state));
  }, [key, state]);

  return [state, setState];
}

/**
 * Fiction Generator - Three-column layout
 * Left 20% - Sidebar, Center 50% - Chat, Right 30% - Output Panel
 */

// Internal component using translation system
function FictionGeneratorApp() {
  const { t, tArray } = useLanguage();
  const [activeTab, setActiveTab] = useLocalStorageState('fiction-generator-active-tab', 'free-form');
  const [outputTab, setOutputTab] = useLocalStorageState('fiction-generator-output-tab', 'new-outputs');
  
  // Fiction type options
  const fictionTypes = tArray('fictionTypes');

  // Form state management
  const [formData, setFormData] = useState({
    storyAbout: "",
    targetAudience: t('audienceOptions.general'),
    fictionType: fictionTypes[0] || t('placeholders.defaultStoryType'),
    chapterCount: 50,
    writingElements: [],
    tone: "",
    inputLanguage: "English",
    outputLanguage: "Chinese (Simplified)"
  });

  const [isGenerating, setIsGenerating] = useState(false);
  
  // 用于演示时间格式的状态
  const [lastTestTime] = useState(new Date(Date.now() - 60 * 1000)); // 1分钟前
  
  // 添加消息时间状态
  const [messageTimes] = useState([
    new Date(Date.now() - 2 * 60 * 1000), // 2分钟前
    new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
    new Date(Date.now() - 10 * 60 * 1000) // 10分钟前
  ]);
  
  // Writing elements options
  const writingElements = tArray('writingElements');
  
  // Clear all inputs
  const clearAllInputs = () => {
    setFormData({
      storyAbout: "",
      targetAudience: t('audienceOptions.general'),
      fictionType: fictionTypes[0] || t('placeholders.defaultStoryType'),
      chapterCount: 50,
      writingElements: [],
      tone: "",
      inputLanguage: "English",
      outputLanguage: "Chinese (Simplified)"
    });
  };
  
  // Generate content
  const handleGenerate = async () => {
    if (!formData.storyAbout.trim()) {
      alert(t('alerts.enterStoryContent'));
      return;
    }
    
    setIsGenerating(true);
    
    // Mock API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      // Here you can integrate actual AI generation API
      console.log('Generating content with:', formData);
      alert(t('alerts.generationSuccess'));
    } catch (error) {
      console.error('Generation failed:', error);
      alert(t('alerts.generationFailed'));
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Handle input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="h-screen bg-gray-50 flex overflow-hidden min-w-[1200px]">
      
      {/* Left sidebar - 20% */}
      <div className="w-1/5 min-w-[350px] bg-white border-r border-gray-200 flex flex-col">
        
        {/* Logo area */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-black rounded-lg flex items-center justify-center">
              <span className="text-2xl">💬</span>
            </div>
            <span className="text-4xl font-semibold text-gray-900">Fictionist</span>
          </div>
        </div>

        {/* Create Fiction button */}
        <div className="p-4 border-b border-gray-100">
          <Button variant="primary" className="w-full">
            + Create Content
          </Button>
        </div>

        {/* Menu area */}
        <div className="flex-1 p-4">
          <Menu>
            <Menu.Group title={t('menu.title')}>
              <Menu.Item 
                icon="📊" 
                onClick={() => console.log('Dashboard clicked')}
              >
                {t('menu.items.dashboard')}
              </Menu.Item>
              
              <Menu.Item 
                icon="📝" 
                onClick={() => console.log('Templates clicked')}
              >
                {t('menu.items.templates')}
              </Menu.Item>
              
              <Menu.Item 
                icon="💬" 
                active={true}
                badge={t('menu.badges.beta')}
                badgeColor="blue"
                onClick={() => console.log('Chat clicked')}
              >
                {t('menu.items.chat')}
              </Menu.Item>
              
              <Menu.Item 
                icon="📄" 
                onClick={() => console.log('Documents clicked')}
              >
                {t('menu.items.documents')}
              </Menu.Item>
              
              <Menu.Item 
                icon="🎨" 
                onClick={() => console.log('Art clicked')}
              >
                {t('menu.items.art')}
              </Menu.Item>
              
              <Menu.Item 
                icon="⚙️" 
                onClick={() => console.log('Settings clicked')}
              >
                {t('menu.items.settings')}
              </Menu.Item>
              
              <Menu.Item 
                icon="❓" 
                onClick={() => console.log('Help clicked')}
              >
                {t('menu.items.help')}
              </Menu.Item>
            </Menu.Group>
          </Menu>
        </div>

        {/* Bottom fixed area */}
        <div className="border-t border-gray-100">
          {/* Trial reminder */}
          <div className="pt-12 px-4 pb-4 bg-gradient-to-br from-yellow-50 to-orange-50 border border-yellow-200 m-4 rounded-lg">
            <div className="text-center mb-3 relative overflow-visible">
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 z-0 w-20 h-6">
                <div className="text-sm absolute top-1 left-1 transform -rotate-12">⭐</div>
                <div className="text-sm absolute top-0 left-1/2 transform -translate-x-1/2">⭐</div>
                <div className="text-sm absolute top-1 right-1 transform rotate-12">⭐</div>
              </div>
              <div className="w-16 h-16 mx-auto mb-2 flex items-center justify-center overflow-visible relative">
                <span className="text-7xl leading-none">👍</span>
              </div>
              <div className="text-sm font-medium text-gray-900 mb-1">{t('trial.endsIn', { days: 4 })}</div>
              <div className="text-xs text-gray-600">{t('trial.planDescription')}</div>
            </div>
            <Button variant="secondary" size="sm" className="w-full text-xs">
              {t('buttons.viewDetails')}
            </Button>
          </div>

          {/* User info */}
          <div className="p-4">
            <div className="p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-3">
                <Avatar name="Drian moreno" size="sm" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900">Drian moreno</div>
                  <div className="text-xs text-gray-500 truncate"><EMAIL></div>
                  {/* 最后测试时间显示 */}
                  <div className="text-xs text-gray-400 mt-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span>{t('time.lastTested')}: {formatRelativeTimeWithTranslation(lastTestTime, t)}</span>
                    </div>
                  </div>
                </div>
                <button className="text-gray-400 hover:text-gray-600">
                  ▼
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Center form area - 25% */}
      <div className="w-1/4 min-w-[450px] bg-white flex flex-col h-full">
        
        {/* Header title area */}
        <div className="p-5 border-b border-gray-100">
          <div className="flex items-center gap-2 mb-1">
            <span className="text-2xl">🗒️</span>
            <h1 className="text-xl font-semibold text-gray-900">{t('appTitle')}</h1>
          </div>
          <p className="text-sm text-gray-600 ml-8">
            {t('content.appDescription')}
          </p>
        </div>

        {/* Form area */}
        <div className="flex-1 flex flex-col min-h-0">
          {/* Scrollable content area */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* Tab switching */}
            <div className="flex gap-4 mb-6">
              <button 
                className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'free-form' 
                    ? 'text-purple-600 bg-purple-50 border border-purple-200' 
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('free-form')}
              >
                {t('sidebar.freeForm')}
              </button>
              <button 
                className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200 ${
                  activeTab === 'templates' 
                    ? 'text-purple-600 bg-purple-50 border border-purple-200' 
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('templates')}
              >
                {t('navigation.templates')}
              </button>
            </div>
            
            <div className="space-y-5">
            
            {/* 📝 基础信息卡片 */}
            <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-lg">📝</span>
                <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.basicInfo')}</h3>
              </div>
              
              {/* Fiction story content */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  {t('form.storyAbout')}
                  <span className="ml-2 text-xs text-gray-500">{formData.storyAbout.length}/400</span>
                </label>
                <div className="relative">
                  <textarea
                    className="w-full h-32 px-4 py-3 pb-12 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm transition-colors"
                    placeholder={t('form.storyPlaceholder')}
                    value={formData.storyAbout}
                    onChange={(e) => handleInputChange('storyAbout', e.target.value)}
                    maxLength={400}
                  />
                  <button 
                    className="absolute bottom-3 right-3 px-3 py-1.5 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg bg-gray-50 border border-gray-200 transition-colors"
                    onClick={() => handleInputChange('storyAbout', '')}
                  >
                    {t('buttons.clear')}
                  </button>
                </div>
              </div>

              {/* Target audience */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  {t('form.targetAudience')}
                </label>
                <div className="flex gap-3">
                  {[
                    { key: 'male', value: t('audienceOptions.male') },
                    { key: 'female', value: t('audienceOptions.female') },
                    { key: 'general', value: t('audienceOptions.general') }
                  ].map(({ key, value }) => (
                    <button
                      key={key}
                      className={`flex-1 px-4 py-3 rounded-xl border text-sm font-medium transition-all duration-200 ${
                        formData.targetAudience === value
                          ? 'bg-purple-600 text-white border-purple-600 shadow-sm'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:text-purple-600 hover:shadow-sm'
                      }`}
                      onClick={() => handleInputChange('targetAudience', value)}
                    >
                      {value}
                    </button>
                  ))}
                </div>
              </div>

              {/* Chapter count */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  {t('form.chapterCount')}
                </label>
                <div className="flex items-center border border-gray-300 rounded-xl overflow-hidden focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-purple-500 transition-colors">
                  <button
                    type="button"
                    className="px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-r border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => handleInputChange('chapterCount', Math.max(1, formData.chapterCount - 1))}
                    disabled={formData.chapterCount <= 1}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                  <input
                    type="number"
                    className="flex-1 px-4 py-3 text-center border-0 focus:outline-none text-sm bg-white"
                    value={formData.chapterCount}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 1;
                      if (value >= 1 && value <= 1000) {
                        handleInputChange('chapterCount', value);
                      }
                    }}
                    min="1"
                    max="1000"
                  />
                  <button
                    type="button"
                    className="px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-l border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => handleInputChange('chapterCount', Math.min(1000, formData.chapterCount + 1))}
                    disabled={formData.chapterCount >= 1000}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-2">{t('form.chapterRange')}</p>
              </div>
            </div>

            {/* 🎭 类型设置卡片 */}
            <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-lg">🎭</span>
                <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.typeSettings')}</h3>
              </div>
              
              <div className="grid grid-cols-1 gap-4">
                {/* Fiction type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    {t('form.fictionType')}
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    {fictionTypes.map((type) => (
                      <button
                        key={type}
                        className={`px-3 py-2.5 text-sm rounded-lg border transition-all duration-200 font-medium ${
                          formData.fictionType === type
                            ? 'bg-purple-500 text-white border-purple-500 shadow-md'
                            : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:text-purple-600 hover:shadow-sm'
                        }`}
                        onClick={() => handleInputChange('fictionType', type)}
                      >
                        {type}
                      </button>
                    ))}
                  </div>
                </div>


              </div>
            </div>

            {/* ✨ 写作元素卡片 */}
            <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-lg">✨</span>
                <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.writingElements')}</h3>
              </div>
              
              {/* Writing elements */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  {t('form.writingElements')}
                </label>
                <div className="grid grid-cols-3 gap-2">
                  {writingElements.map((element) => (
                    <button
                      key={element}
                      className={`px-3 py-2.5 rounded-xl border text-xs font-medium transition-all duration-200 ${
                        formData.writingElements.includes(element)
                          ? 'bg-purple-600 text-white border-purple-600 shadow-sm'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:text-purple-600 hover:shadow-sm'
                      }`}
                      onClick={() => {
                        const elements = formData.writingElements.includes(element)
                          ? formData.writingElements.filter(e => e !== element)
                          : [...formData.writingElements, element];
                        handleInputChange('writingElements', elements);
                      }}
                    >
                      {element}
                    </button>
                  ))}
                </div>
              </div>

              {/* Tone style */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  {t('form.toneStyle')}
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm transition-colors"
                  placeholder={t('form.tonePlaceholder')}
                  value={formData.tone}
                  onChange={(e) => handleInputChange('tone', e.target.value)}
                />
              </div>
            </div>

            {/* 🌍 语言设置卡片 */}
            <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-lg">🌍</span>
                <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.languageSettings')}</h3>
              </div>
              
              {/* Language options */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  {t('form.languageOptions')}
                </label>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-600 mb-2">{t('form.inputLanguage')}</label>
                    <select 
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                      value={formData.inputLanguage}
                      onChange={(e) => handleInputChange('inputLanguage', e.target.value)}
                    >
                      <option>English</option>
                      <option>Chinese</option>
                      <option>Spanish</option>
                      <option>French</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-600 mb-2">{t('form.outputLanguage')}</label>
                    <select 
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                      value={formData.outputLanguage}
                      onChange={(e) => handleInputChange('outputLanguage', e.target.value)}
                    >
                      <option>Chinese (Simplified)</option>
                      <option>Chinese (Traditional)</option>
                      <option>English (American)</option>
                      <option>English (British)</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            </div>
          </div>

          {/* Fixed bottom button area */}
          <div className="border-t border-gray-200 p-6 bg-white shadow-lg backdrop-blur-sm bg-white/95">
            <div className="flex items-center justify-between">
              <button 
                className="flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700 transition-colors"
                onClick={clearAllInputs}
                disabled={isGenerating}
              >
                <span>✗</span>
                {t('buttons.clearAll')}
              </button>
              <Button 
                variant="primary" 
                className="px-6"
                onClick={handleGenerate}
                disabled={isGenerating || !formData.storyAbout.trim()}
              >
                <span className="mr-2">✨</span>
                {isGenerating ? t('buttons.generating') : t('buttons.generate')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Right chat window - flexible (rest of space) */}
      <div className="flex-1 min-w-[400px] bg-white border-l border-gray-200 flex flex-col">
        
        {/* Output tabs */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center gap-4">
            <div className="flex gap-4">
            <button 
              className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200 ${
                outputTab === 'new-outputs' 
                  ? 'text-blue-600 bg-blue-50 border border-blue-200' 
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
              onClick={() => setOutputTab('new-outputs')}
            >
              {t('rightPanel.newOutputs')}
            </button>
            <button 
              className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-lg transition-colors duration-200 ${
                outputTab === 'history' 
                  ? 'text-blue-600 bg-blue-50 border border-blue-200' 
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              }`}
              onClick={() => setOutputTab('history')}
            >
              {t('rightPanel.history')}
            </button>
            </div>
            <LanguageSwitcher className="flex items-center gap-2 ml-auto" />
          </div>
        </div>

        {/* Output content */}
        <div className="flex-1 overflow-y-auto">
          {outputTab === 'new-outputs' ? (
            <div className="p-4 space-y-4">
              
              {/* Output item 1 */}
              <Card padding="md" className="border border-gray-200">
                <div className="mb-3">
                  <p className="text-sm text-gray-900 leading-relaxed mb-2">
                    "In recent years, artificial intelligence (AI) has revolutionized numerous industries, from healthcare to finance, by leveraging advanced algorithms to analyze vast amounts of data and extract meaningful insights. This transformative technology has the potential to streamline processes, enhance decision-making, and drive innovation across various sectors. However, with these advancements come ethical considerations regarding privacy, bias, and job displacement. As AI continues to evolve, it is crucial for policymakers, researchers, and industry leaders to collaborate in establishing robust frameworks that ensure its responsible and equitable deployment, fostering a future where AI benefits society as a whole..."
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">{formatRelativeTimeWithTranslation(messageTimes[0], t)}</span>
                    <div className="flex gap-1">
                      <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                        <span className="text-sm">📄</span>
                      </button>
                      <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                        <span className="text-sm">👍</span>
                      </button>
                      <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                        <span className="text-sm">👎</span>
                      </button>
                    </div>
                  </div>
                </div>

              </Card>

              {/* Output item 2 */}
              <Card padding="md" className="border border-gray-200">
                <div className="mb-3">
                  <p className="text-sm text-gray-900 leading-relaxed mb-2">
                    "In recent years, artificial intelligence (AI) has revolutionized numerous industries, from healthcare to finance, by leveraging advanced algorithms to analyze vast amounts of data and extract meaningful insights. This transformative technology has the potential to streamline processes, enhance decision-making, and drive innovation across various sectors. However, with these advancements come ethical considerations regarding privacy, bias, and job displacement. As AI continues to evolve, it is crucial..."
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">{formatRelativeTimeWithTranslation(messageTimes[1], t)}</span>
                    <div className="flex gap-1">
                      <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                        <span className="text-sm">📄</span>
                      </button>
                      <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                        <span className="text-sm">👍</span>
                      </button>
                      <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                        <span className="text-sm">👎</span>
                      </button>
                    </div>
                  </div>
                </div>

              </Card>

              {/* Output item 3 */}
              <Card padding="md" className="border border-gray-200">
                <div className="mb-3">
                  <p className="text-sm text-gray-900 leading-relaxed mb-2">
                    Amounts of data and extract meaningful insights. This transformative technology has the potential to streamline processes, enhance decision-making, and drive innovation across various sectors. However, with these advancements come ethical considerations regarding privacy, bias, and job displacement. As AI continues to evolve, it is crucial.
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-500">{formatRelativeTimeWithTranslation(messageTimes[2], t)}</span>
                    <div className="flex gap-1">
                      <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                        <span className="text-sm">📄</span>
                      </button>
                      <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                        <span className="text-sm">👍</span>
                      </button>
                      <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                        <span className="text-sm">👎</span>
                      </button>
                    </div>
                  </div>
                </div>

              </Card>

            </div>
          ) : (
            <div className="p-4">
              <div className="text-center text-gray-500 text-sm">
                <p>No history available</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Main App component with LanguageProvider wrapper
function App() {
  return (
    <LanguageProvider>
      <FictionGeneratorApp />
    </LanguageProvider>
  );
}

export default App;