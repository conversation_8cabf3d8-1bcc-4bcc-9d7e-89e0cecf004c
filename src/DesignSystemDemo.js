import React from 'react';
import { But<PERSON>, <PERSON>, Avatar } from './design-system';
import mockData from './mock/chatData';

/**
 * 设计系统演示组件
 * 展示所有基础组件和设计令牌的使用示例
 * 保留作为设计规范参考
 */
function DesignSystemDemo() {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* 顶部标题 */}
      <div className="max-w-7xl mx-auto mb-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI Chat Interface Design System
          </h1>
          <p className="text-gray-600">
            基于现代 AI 聊天界面的设计系统演示
          </p>
        </div>

        {/* 设计系统演示 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* 左侧：组件展示 */}
          <div className="lg:col-span-1 space-y-6">
            
            {/* 按钮组件演示 */}
            <Card padding="lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Button Components
              </h3>
              <div className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  <Button variant="primary">Primary</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="danger">Danger</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button size="sm">Small</Button>
                  <Button size="md">Medium</Button>
                  <Button size="lg">Large</Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button loading>Loading</Button>
                  <Button disabled>Disabled</Button>
                </div>
              </div>
            </Card>

            {/* 头像组件演示 */}
            <Card padding="lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Avatar Components
              </h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar 
                    src={mockData.user.avatar}
                    name={mockData.user.name}
                    size="xs"
                  />
                  <Avatar 
                    src={mockData.user.avatar}
                    name={mockData.user.name}
                    size="sm"
                  />
                  <Avatar 
                    src={mockData.user.avatar}
                    name={mockData.user.name}
                    size="md"
                  />
                  <Avatar 
                    src={mockData.user.avatar}
                    name={mockData.user.name}
                    size="lg"
                    online
                  />
                </div>
                <div className="flex items-center gap-3">
                  <Avatar name="AI" shape="rounded" />
                  <Avatar name="User" shape="circle" />
                  <Avatar name="Bot" shape="square" />
                </div>
              </div>
            </Card>

            {/* 色彩演示 */}
            <Card padding="lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Color System
              </h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary-500 rounded-lg"></div>
                  <span className="text-sm">Primary Blue</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-lg border"></div>
                  <span className="text-sm">Background Gray</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded-lg"></div>
                  <span className="text-sm">Success Green</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-red-500 rounded-lg"></div>
                  <span className="text-sm">Error Red</span>
                </div>
              </div>
            </Card>
          </div>

          {/* 中间和右侧：聊天界面演示 */}
          <div className="lg:col-span-2">
            <Card padding="none" className="h-96 lg:h-[600px] flex">
              
              {/* 左侧面板：聊天历史 */}
              <div className="w-80 border-r border-gray-200 flex flex-col">
                {/* 头部 */}
                <div className="p-4 border-b border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="font-semibold text-gray-900">Chat Results</h2>
                    <Button variant="ghost" size="icon">✕</Button>
                  </div>
                  <Button variant="primary" size="sm" className="w-full">
                    + New Chat
                  </Button>
                </div>

                {/* 历史记录 */}
                <div className="flex-1 overflow-y-auto scrollbar-thin p-4">
                  <div className="space-y-4">
                    <div className="text-sm font-medium text-gray-600 mb-2">Today</div>
                    {mockData.history.slice(0, 1).map((chat) => (
                      <Card 
                        key={chat.id} 
                        padding="sm" 
                        hoverable 
                        className="cursor-pointer"
                      >
                        <div className="flex items-start gap-3">
                          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            🖼️
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-gray-900 text-sm">
                              {chat.title}
                            </h4>
                            <p className="text-xs text-gray-500 mt-1">
                              {chat.date}
                            </p>
                          </div>
                        </div>
                      </Card>
                    ))}
                    
                    <div className="text-sm font-medium text-gray-600 mb-2 mt-6">Yesterday</div>
                    {mockData.history.slice(1).map((chat) => (
                      <Card 
                        key={chat.id} 
                        padding="sm" 
                        hoverable 
                        className="cursor-pointer"
                      >
                        <div className="flex items-start gap-3">
                          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            {chat.type === 'search' ? '🔍' : '📊'}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-gray-900 text-sm">
                              {chat.title}
                            </h4>
                            <p className="text-xs text-gray-600 mt-1 text-ellipsis-2">
                              {chat.subtitle}
                            </p>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>

              {/* 右侧面板：当前对话 */}
              <div className="flex-1 flex flex-col">
                {/* 聊天头部 */}
                <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                  <h2 className="font-semibold text-gray-900">New Chat</h2>
                  <Button variant="ghost" size="icon">✕</Button>
                </div>

                {/* 消息区域 */}
                <div className="flex-1 overflow-y-auto scrollbar-thin p-4 space-y-4">
                  {mockData.currentChat.messages.map((message) => (
                    <div 
                      key={message.id}
                      className={`flex gap-3 ${message.sender.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      {message.sender.role === 'assistant' && (
                        <Avatar 
                          name={message.sender.name}
                          size="sm"
                          shape="rounded"
                        />
                      )}
                      
                      <div className={`max-w-md ${message.sender.role === 'user' ? 'order-first' : ''}`}>
                        <div className={`message-bubble ${message.sender.role === 'user' ? 'user' : 'ai'}`}>
                          <div className="whitespace-pre-wrap text-sm">
                            {message.content}
                          </div>
                          
                          {message.attachments && (
                            <div className="mt-2 p-2 bg-white/10 rounded-lg">
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 bg-red-500 rounded flex items-center justify-center text-xs text-white">
                                  📄
                                </div>
                                <span className="text-xs">Document.pdf</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {message.sender.role === 'user' && (
                        <Avatar 
                          src={message.sender.avatar}
                          name={message.sender.name}
                          size="sm"
                        />
                      )}
                    </div>
                  ))}
                </div>

                {/* 工具栏 */}
                <div className="p-4 border-t border-gray-200">
                  <div className="flex gap-2 mb-3">
                    {mockData.tools.map((tool) => (
                      <Button 
                        key={tool.id}
                        variant="secondary" 
                        size="sm"
                        className="text-xs"
                      >
                        {tool.icon} {tool.name}
                      </Button>
                    ))}
                  </div>
                  
                  {/* 输入框 */}
                  <div className="flex gap-2">
                    <input 
                      type="text" 
                      placeholder="Ask me anything..."
                      className="input-field flex-1"
                    />
                    <Button variant="primary">
                      ↑
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* 底部说明 */}
        <div className="mt-8 text-center">
          <Card padding="lg" className="max-w-2xl mx-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              🎯 设计系统特点
            </h3>
            <div className="text-sm text-gray-600 space-y-2">
              <p>• <strong>模块化设计</strong>：基础组件 + 组合模式 + 布局组件</p>
              <p>• <strong>一致的色彩</strong>：基于淡紫灰背景的现代配色方案</p>
              <p>• <strong>灵活配置</strong>：通过 props 控制组件的各种变体</p>
              <p>• <strong>响应式布局</strong>：适配移动端和桌面端</p>
              <p>• <strong>易于维护</strong>：清晰的文件结构和命名规范</p>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default DesignSystemDemo;
