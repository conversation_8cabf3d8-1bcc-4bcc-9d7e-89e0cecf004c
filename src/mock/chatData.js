/**
 * Chat 界面模拟数据
 */

export const mockUser = {
  id: 'user-1',
  name: '<PERSON><PERSON>',
  avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
  role: 'user'
};

export const mockAI = {
  id: 'ai-assistant',
  name: 'AI Assistant',
  avatar: null,
  role: 'assistant'
};

export const mockChatHistory = [
  {
    id: 'chat-1',
    title: 'Image Generation',
    subtitle: 'Parrot images',
    date: 'Today • 16 October',
    type: 'image',
    thumbnail: 'https://images.unsplash.com/photo-1552728089-57bdde30beb3?w=300&h=200&fit=crop',
    status: 'completed'
  },
  {
    id: 'chat-2', 
    title: 'AI Search',
    subtitle: 'How to decrease CAC?',
    date: 'Yesterday • 15 October',
    type: 'search',
    status: 'completed'
  },
  {
    id: 'chat-3',
    title: 'Data Analysis',
    subtitle: 'How to increase LTV?',
    date: 'Yesterday • 15 October', 
    type: 'analysis',
    status: 'completed'
  }
];

export const mockCurrentChat = {
  id: 'current-chat',
  title: 'New Chat',
  messages: [
    {
      id: 'msg-1',
      sender: mockAI,
      content: 'Hi, Marry!\nHow can I help you?',
      timestamp: '2024-01-15T10:30:00Z',
      type: 'text'
    },
    {
      id: 'msg-2',
      sender: mockUser,
      content: 'Imagine that you are the manager and make me the list of summary points of this documents',
      timestamp: '2024-01-15T10:32:00Z',
      type: 'text',
      attachments: [
        {
          id: 'file-1',
          name: 'Document.pdf',
          type: 'pdf',
          size: '2.3 MB',
          url: '#'
        }
      ]
    },
    {
      id: 'msg-3',
      sender: mockAI,
      content: `Based on the document you've shared, here are the key summary points:

**1. Resilient Tourism:** Despite the pandemic's impact, international tourism rebounded significantly in 2022, showing the industry's resilience.

**2. Growth Predictions:** Forecasts suggest that travel and tourism GDP will grow at 5.8% annually between 2022 and 2032, outpacing overall economic growth.`,
      timestamp: '2024-01-15T10:35:00Z',
      type: 'text'
    }
  ]
};

export const mockTools = [
  {
    id: 'chat-files',
    name: 'Chat Files',
    icon: '📄',
    description: 'Upload and analyze files'
  },
  {
    id: 'images',
    name: 'Images', 
    icon: '🖼️',
    description: 'Generate and edit images'
  },
  {
    id: 'translate',
    name: 'Translate',
    icon: '🌐',
    description: 'Translate text to any language'
  },
  {
    id: 'audio-chat',
    name: 'Audio Chat',
    icon: '🎤',
    description: 'Voice conversation'
  }
];

const chatData = {
  user: mockUser,
  ai: mockAI,
  history: mockChatHistory,
  currentChat: mockCurrentChat,
  tools: mockTools
};

export default chatData;
