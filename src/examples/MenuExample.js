import React, { useState } from 'react';
import { Menu } from '../design-system';
import { useLanguage } from '../LanguageContext';

/**
 * Menu Component 使用示例
 * 展示菜单组件的各种功能和状态
 */
const MenuExample = () => {
  const { t } = useLanguage();
  const [activeMenuItem, setActiveMenuItem] = useState('chat');

  const handleMenuClick = (itemKey) => {
    setActiveMenuItem(itemKey);
    console.log(`Clicked: ${itemKey}`);
  };

  return (
    <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Menu Component 示例</h2>
      
      {/* 主要菜单组 */}
      <Menu className="mb-6">
        <Menu.Group title={t('menu.title')}>
          <Menu.Item 
            icon="📊" 
            active={activeMenuItem === 'dashboard'}
            onClick={() => handleMenuClick('dashboard')}
          >
            {t('menu.items.dashboard')}
          </Menu.Item>
          
          <Menu.Item 
            icon="📝" 
            active={activeMenuItem === 'templates'}
            onClick={() => handleMenuClick('templates')}
          >
            {t('menu.items.templates')}
          </Menu.Item>
          
          <Menu.Item 
            icon="💬" 
            active={activeMenuItem === 'chat'}
            badge={t('menu.badges.beta')}
            badgeColor="blue"
            onClick={() => handleMenuClick('chat')}
          >
            {t('menu.items.chat')}
          </Menu.Item>
          
          <Menu.Item 
            icon="📄" 
            active={activeMenuItem === 'documents'}
            onClick={() => handleMenuClick('documents')}
          >
            {t('menu.items.documents')}
          </Menu.Item>
          
          <Menu.Item 
            icon="🎨" 
            active={activeMenuItem === 'art'}
            badge={t('menu.badges.new')}
            badgeColor="green"
            onClick={() => handleMenuClick('art')}
          >
            {t('menu.items.art')}
          </Menu.Item>
        </Menu.Group>
      </Menu>

      {/* 设置菜单组 */}
      <Menu>
        <Menu.Group title="SETTINGS">
          <Menu.Item 
            icon="⚙️" 
            active={activeMenuItem === 'settings'}
            onClick={() => handleMenuClick('settings')}
          >
            {t('menu.items.settings')}
          </Menu.Item>
          
          <Menu.Item 
            icon="❓" 
            active={activeMenuItem === 'help'}
            onClick={() => handleMenuClick('help')}
          >
            {t('menu.items.help')}
          </Menu.Item>
          
          <Menu.Item 
            icon="👤" 
            active={activeMenuItem === 'account'}
            badge={t('menu.badges.pro')}
            badgeColor="purple"
            onClick={() => handleMenuClick('account')}
          >
            {t('menu.items.account')}
          </Menu.Item>
        </Menu.Group>
      </Menu>

      {/* 当前选中项显示 */}
      <div className="mt-6 p-3 bg-gray-50 rounded">
        <p className="text-sm text-gray-600">
          当前选中: <span className="font-medium">{activeMenuItem}</span>
        </p>
      </div>
    </div>
  );
};

export default MenuExample;
