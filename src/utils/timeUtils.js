/**
 * 时间格式化工具函数
 * 支持中英文不同的时间显示格式，使用翻译系统
 */

/**
 * 格式化相对时间（使用翻译系统）
 * @param {Date} date - 要格式化的日期
 * @param {Function} t - 翻译函数
 * @returns {string} 格式化后的时间字符串
 */
export const formatRelativeTimeWithTranslation = (date, t) => {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);
  const diffInWeeks = Math.floor(diffInDays / 7);
  const diffInMonths = Math.floor(diffInDays / 30);
  const diffInYears = Math.floor(diffInDays / 365);

  if (diffInSeconds < 60) {
    return t('time.justNow');
  } else if (diffInMinutes < 60) {
    return diffInMinutes === 1 
      ? t('time.oneMinuteAgo')
      : t('time.minutesAgo', { minutes: diffInMinutes });
  } else if (diffInHours < 24) {
    return diffInHours === 1 
      ? t('time.oneHourAgo')
      : t('time.hoursAgo', { hours: diffInHours });
  } else if (diffInDays < 7) {
    return diffInDays === 1 
      ? t('time.oneDayAgo')
      : t('time.daysAgo', { days: diffInDays });
  } else if (diffInWeeks < 4) {
    return diffInWeeks === 1 
      ? t('time.oneWeekAgo')
      : t('time.weeksAgo', { weeks: diffInWeeks });
  } else if (diffInMonths < 12) {
    return diffInMonths === 1 
      ? t('time.oneMonthAgo')
      : t('time.monthsAgo', { months: diffInMonths });
  } else {
    return diffInYears === 1 
      ? t('time.oneYearAgo')
      : t('time.yearsAgo', { years: diffInYears });
  }
};

/**
 * 格式化相对时间（不使用翻译系统的版本，保持向后兼容）
 * @param {Date} date - 要格式化的日期
 * @param {string} language - 语言 ('zh' | 'en' | 'ja' | 'ko')
 * @returns {string} 格式化后的时间字符串
 */
export const formatRelativeTime = (date, language = 'zh') => {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);
  const diffInWeeks = Math.floor(diffInDays / 7);
  const diffInMonths = Math.floor(diffInDays / 30);
  const diffInYears = Math.floor(diffInDays / 365);

  if (language === 'zh') {
    // 中文格式
    if (diffInSeconds < 60) {
      return '刚刚';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`;
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInDays < 7) {
      return `${diffInDays}天前`;
    } else if (diffInWeeks < 4) {
      return `${diffInWeeks}周前`;
    } else if (diffInMonths < 12) {
      return `${diffInMonths}个月前`;
    } else {
      return `${diffInYears}年前`;
    }
  } else if (language === 'ja') {
    // 日语格式
    if (diffInSeconds < 60) {
      return 'たった今';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}分前`;
    } else if (diffInHours < 24) {
      return `${diffInHours}時間前`;
    } else if (diffInDays < 7) {
      return `${diffInDays}日前`;
    } else if (diffInWeeks < 4) {
      return `${diffInWeeks}週間前`;
    } else if (diffInMonths < 12) {
      return `${diffInMonths}ヶ月前`;
    } else {
      return `${diffInYears}年前`;
    }
  } else if (language === 'ko') {
    // 韩语格式
    if (diffInSeconds < 60) {
      return '방금 전';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}분 전`;
    } else if (diffInHours < 24) {
      return `${diffInHours}시간 전`;
    } else if (diffInDays < 7) {
      return `${diffInDays}일 전`;
    } else if (diffInWeeks < 4) {
      return `${diffInWeeks}주 전`;
    } else if (diffInMonths < 12) {
      return `${diffInMonths}개월 전`;
    } else {
      return `${diffInYears}년 전`;
    }
  } else {
    // 英文格式
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInMinutes < 60) {
      return diffInMinutes === 1 ? '1 minute ago' : `${diffInMinutes} minutes ago`;
    } else if (diffInHours < 24) {
      return diffInHours === 1 ? '1 hour ago' : `${diffInHours} hours ago`;
    } else if (diffInDays < 7) {
      return diffInDays === 1 ? '1 day ago' : `${diffInDays} days ago`;
    } else if (diffInWeeks < 4) {
      return diffInWeeks === 1 ? '1 week ago' : `${diffInWeeks} weeks ago`;
    } else if (diffInMonths < 12) {
      return diffInMonths === 1 ? '1 month ago' : `${diffInMonths} months ago`;
    } else {
      return diffInYears === 1 ? '1 year ago' : `${diffInYears} years ago`;
    }
  }
};

/**
 * 格式化绝对时间
 * @param {Date} date - 要格式化的日期
 * @param {string} language - 语言 ('zh' | 'en' | 'ja' | 'ko')
 * @param {boolean} includeTime - 是否包含时间
 * @returns {string} 格式化后的时间字符串
 */
export const formatAbsoluteTime = (date, language = 'zh', includeTime = true) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  if (language === 'zh') {
    // 中文格式
    if (includeTime) {
      return `${year}年${month}月${day}日 ${hours}:${minutes}`;
    } else {
      return `${year}年${month}月${day}日`;
    }
  } else if (language === 'ja') {
    // 日语格式
    if (includeTime) {
      return `${year}年${month}月${day}日 ${hours}:${minutes}`;
    } else {
      return `${year}年${month}月${day}日`;
    }
  } else if (language === 'ko') {
    // 韩语格式
    if (includeTime) {
      return `${year}년 ${month}월 ${day}일 ${hours}:${minutes}`;
    } else {
      return `${year}년 ${month}월 ${day}일`;
    }
  } else {
    // 英文格式
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    };
    
    if (includeTime) {
      options.hour = '2-digit';
      options.minute = '2-digit';
      options.hour12 = true;
    }
    
    return date.toLocaleDateString('en-US', options);
  }
};

/**
 * 获取友好的时间显示（优先显示相对时间，超过一定时间后显示绝对时间）
 * @param {Date} date - 要格式化的日期
 * @param {string} language - 语言 ('zh' | 'en' | 'ja' | 'ko')
 * @returns {string} 格式化后的时间字符串
 */
export const formatFriendlyTime = (date, language = 'zh') => {
  const now = new Date();
  const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
  
  // 7天内显示相对时间，超过7天显示绝对时间
  if (diffInDays < 7) {
    return formatRelativeTime(date, language);
  } else {
    return formatAbsoluteTime(date, language, false);
  }
};

/**
 * 格式化试用期剩余时间
 * @param {number} remainingDays - 剩余天数
 * @param {string} language - 语言 ('zh' | 'en' | 'ja' | 'ko')
 * @returns {string} 格式化后的时间字符串
 */
export const formatTrialTime = (remainingDays, language = 'zh') => {
  if (language === 'zh') {
    if (remainingDays <= 0) {
      return '试用期已过期';
    } else if (remainingDays === 1) {
      return '试用期还有1天';
    } else {
      return `试用期还有${remainingDays}天`;
    }
  } else if (language === 'ja') {
    if (remainingDays <= 0) {
      return 'トライアル期間が終了しました';
    } else if (remainingDays === 1) {
      return 'トライアル期間は1日残っています';
    } else {
      return `トライアル期間は${remainingDays}日残っています`;
    }
  } else if (language === 'ko') {
    if (remainingDays <= 0) {
      return '체험 기간이 만료되었습니다';
    } else if (remainingDays === 1) {
      return '체험 기간이 1일 남았습니다';
    } else {
      return `체험 기간이 ${remainingDays}일 남았습니다`;
    }
  } else {
    if (remainingDays <= 0) {
      return 'Trial period expired';
    } else if (remainingDays === 1) {
      return 'Trial ends in 1 day';
    } else {
      return `Trial ends in ${remainingDays} days`;
    }
  }
};
