import React, { createContext, useContext, useState, useEffect } from 'react';
import translations from './translations.json';

// 创建语言上下文
const LanguageContext = createContext();

// 支持的语言列表
const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文', nativeName: '中文' },
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ja', name: 'Japanese', nativeName: '日本語' },
  { code: 'ko', name: 'Korean', nativeName: '한국어' }
];

// 语言提供者组件
export const LanguageProvider = ({ children }) => {
  // 从本地存储读取语言设置，没有则默认中文
  const [language, setLanguage] = useState(() => {
    try {
      const saved = localStorage.getItem('fiction-generator-language');
      const savedLang = saved ? JSON.parse(saved) : 'zh';
      // 验证保存的语言是否在支持列表中
      return SUPPORTED_LANGUAGES.some(lang => lang.code === savedLang) ? savedLang : 'zh';
    } catch {
      return 'zh';
    }
  });

  // 保存语言设置到本地存储
  useEffect(() => {
    localStorage.setItem('fiction-generator-language', JSON.stringify(language));
  }, [language]);

  // 切换语言函数 (保持向后兼容)
  const toggleLanguage = () => {
    const currentIndex = SUPPORTED_LANGUAGES.findIndex(lang => lang.code === language);
    const nextIndex = (currentIndex + 1) % SUPPORTED_LANGUAGES.length;
    setLanguage(SUPPORTED_LANGUAGES[nextIndex].code);
  };

  // 新增：设置特定语言函数
  const changeLanguage = (langCode) => {
    if (SUPPORTED_LANGUAGES.some(lang => lang.code === langCode)) {
      setLanguage(langCode);
    }
  };

  // 新增：获取支持的语言列表
  const getSupportedLanguages = () => SUPPORTED_LANGUAGES;

  // 新增：获取当前语言信息
  const getCurrentLanguageInfo = () => {
    return SUPPORTED_LANGUAGES.find(lang => lang.code === language) || SUPPORTED_LANGUAGES[0];
  };

  // 获取翻译文本的函数（支持插值）
  const t = (path, params = {}) => {
    const keys = path.split('.');
    let result = translations;
    
    for (const key of keys) {
      result = result[key];
      if (!result) {
        console.warn(`Translation key not found: ${path}`);
        return path;
      }
    }
    
    let text = result[language] || result.zh || path;
    
    // 支持参数插值，替换 {{key}} 形式的占位符
    if (typeof text === 'string' && Object.keys(params).length > 0) {
      Object.keys(params).forEach(key => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        text = text.replace(regex, params[key]);
      });
    }
    
    return text;
  };

  // 获取数组类型的翻译（如小说类型、写作元素）
  const tArray = (path) => {
    const keys = path.split('.');
    let result = translations;
    
    for (const key of keys) {
      result = result[key];
      if (!result) {
        console.warn(`Translation key not found: ${path}`);
        return [];
      }
    }
    
    return result[language] || result.zh || [];
  };

  const value = {
    language,
    setLanguage,
    toggleLanguage,
    changeLanguage,
    getSupportedLanguages,
    getCurrentLanguageInfo,
    t,
    tArray
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// 自定义Hook来使用语言上下文
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
