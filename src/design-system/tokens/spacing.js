/**
 * 设计令牌 - 间距系统
 * 基于 8px 网格系统
 */

export const spacing = {
  // 基础间距 (8px 为基础单位)
  0: '0',
  1: '0.25rem',  // 4px
  2: '0.5rem',   // 8px
  3: '0.75rem',  // 12px
  4: '1rem',     // 16px
  5: '1.25rem',  // 20px
  6: '1.5rem',   // 24px
  8: '2rem',     // 32px
  10: '2.5rem',  // 40px
  12: '3rem',    // 48px
  16: '4rem',    // 64px
  20: '5rem',    // 80px
  24: '6rem',    // 96px

  // 语义化间距
  xs: '0.5rem',    // 8px - 最小间距
  sm: '0.75rem',   // 12px - 小间距
  md: '1rem',      // 16px - 中等间距
  lg: '1.5rem',    // 24px - 大间距
  xl: '2rem',      // 32px - 超大间距
  '2xl': '3rem',   // 48px - 特大间距
  '3xl': '4rem',   // 64px - 巨大间距
};

// 组件专用间距
export const componentSpacing = {
  // 按钮内边距
  button: {
    sm: { x: '0.75rem', y: '0.375rem' },  // 12px, 6px
    md: { x: '1rem', y: '0.5rem' },       // 16px, 8px
    lg: { x: '1.5rem', y: '0.75rem' }     // 24px, 12px
  },

  // 卡片内边距
  card: {
    sm: '1rem',      // 16px
    md: '1.5rem',    // 24px
    lg: '2rem'       // 32px
  },

  // 面板间距
  panel: {
    padding: '1.5rem',     // 24px
    gap: '1rem'            // 16px
  },

  // 消息间距
  message: {
    gap: '0.75rem',        // 12px - 消息之间
    padding: '0.75rem'     // 12px - 消息内边距
  }
};

// 布局间距
export const layoutSpacing = {
  // 容器间距
  container: {
    sm: '1rem',      // 16px - 手机
    md: '1.5rem',    // 24px - 平板
    lg: '2rem'       // 32px - 桌面
  },

  // 栅格间距
  grid: {
    gap: '1rem',     // 16px - 栅格间隙
    gutter: '1.5rem' // 24px - 栅格边距
  },

  // 侧边栏
  sidebar: {
    width: '20rem',    // 320px - 侧边栏宽度
    collapsed: '4rem'  // 64px - 收起宽度
  }
};

export default spacing;
