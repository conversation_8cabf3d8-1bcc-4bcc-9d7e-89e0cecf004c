/**
 * 设计令牌 - 颜色系统
 * 基于 AI Chat 界面的色彩分析
 */

export const colors = {
  // 主色调 - 蓝色系
  primary: {
    50: '#EFF6FF',   // 最浅蓝 - 背景高亮
    100: '#DBEAFE',  // 浅蓝 - 悬停背景
    500: '#3B82F6',  // 主蓝色 - 按钮、链接
    600: '#2563EB',  // 深蓝 - 按钮悬停
    900: '#1E3A8A'   // 最深蓝 - 强调文字
  },

  // 中性色 - 灰色系
  neutral: {
    50: '#F9FAFB',   // 页面背景
    100: '#F3F4F6',  // 卡片背景、面板背景
    200: '#E5E7EB',  // 分割线、边框
    300: '#D1D5DB',  // 禁用状态
    400: '#9CA3AF',  // 辅助文字、图标
    500: '#6B7280',  // 次要文字
    600: '#4B5563',  // 主要文字
    700: '#374151',  // 标题文字
    800: '#1F2937',  // 深色文字
    900: '#111827'   // 最深文字
  },

  // 功能色
  semantic: {
    success: '#10B981',  // 成功状态
    warning: '#F59E0B',  // 警告状态
    error: '#EF4444',    // 错误状态
    info: '#3B82F6'      // 信息状态
  },

  // 特殊用途色彩
  special: {
    userMessage: '#3B82F6',    // 用户消息气泡
    aiMessage: '#F3F4F6',      // AI消息气泡
    codeBlock: '#1F2937',      // 代码块背景
    highlight: '#FEF3C7'       // 高亮背景
  }
};

// 色彩使用指南
export const colorUsage = {
  text: {
    primary: colors.neutral[900],     // 主要文字
    secondary: colors.neutral[600],   // 次要文字
    muted: colors.neutral[400],       // 辅助文字
    inverse: '#FFFFFF'                // 反色文字
  },
  
  background: {
    primary: '#FFFFFF',               // 主背景
    secondary: colors.neutral[50],    // 次要背景
    tertiary: colors.neutral[100],    // 第三背景
    elevated: '#FFFFFF'               // 卡片背景
  },
  
  border: {
    default: colors.neutral[200],     // 默认边框
    muted: colors.neutral[100],       // 弱边框
    strong: colors.neutral[300]       // 强边框
  }
};

export default colors;
