/**
 * 设计令牌 - 阴影系统
 * 基于材料设计的阴影层次
 */

export const shadows = {
  // 基础阴影
  none: 'none',
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',           // 轻微阴影
  base: '0 1px 3px 0 rgb(0 0 0 / 0.1)',          // 默认阴影
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',         // 中等阴影
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',       // 大阴影
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)',       // 超大阴影
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',  // 巨大阴影

  // 组件专用阴影
  card: '0 1px 3px 0 rgb(0 0 0 / 0.1)',          // 卡片阴影
  hover: '0 4px 6px -1px rgb(0 0 0 / 0.1)',      // 悬停阴影
  active: '0 2px 4px 0 rgb(0 0 0 / 0.1)',        // 激活阴影
  focus: '0 0 0 3px rgb(59 130 246 / 0.1)',      // 焦点阴影
  
  // 特殊阴影
  modal: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04)',
  dropdown: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)',
  tooltip: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06)',
  
  // 内阴影
  inset: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.06)',
  
  // 彩色阴影
  colored: {
    blue: '0 4px 6px -1px rgb(59 130 246 / 0.1)',
    green: '0 4px 6px -1px rgb(16 185 129 / 0.1)',
    red: '0 4px 6px -1px rgb(239 68 68 / 0.1)',
    yellow: '0 4px 6px -1px rgb(245 158 11 / 0.1)'
  }
};

// 阴影使用指南
export const shadowUsage = {
  // 界面层次
  flat: shadows.none,           // 平面元素
  raised: shadows.base,         // 略微抬起
  floating: shadows.md,         // 浮动元素
  overlay: shadows.lg,          // 覆盖层
  modal: shadows.modal,         // 模态框

  // 交互状态
  rest: shadows.card,           // 静止状态
  hover: shadows.hover,         // 悬停状态
  active: shadows.active,       // 激活状态
  focus: shadows.focus,         // 焦点状态

  // 组件类型
  card: shadows.card,           // 卡片组件
  button: shadows.sm,           // 按钮组件
  input: shadows.inset,         // 输入框
  dropdown: shadows.dropdown,   // 下拉菜单
  tooltip: shadows.tooltip      // 工具提示
};

export default shadows;
