/**
 * Design System 统一导出
 * 
 * 使用方式:
 * import { Button, Card, Avatar } from '../design-system'
 */

// 设计令牌
import colorsToken, { colorUsage } from './tokens/colors';
import spacingToken, { componentSpacing, layoutSpacing } from './tokens/spacing';
import shadowsToken, { shadowUsage } from './tokens/shadows';

export { colorsToken as colors, colorUsage };
export { spacingToken as spacing, componentSpacing, layoutSpacing };
export { shadowsToken as shadows, shadowUsage };

// 基础组件 (Foundations)
export { default as Button } from './foundations/Button';
export { default as Card, ChatCard, HistoryCard, ToolCard } from './foundations/Card';
export { default as Avatar, UserAvatar, AIAvatar } from './foundations/Avatar';
export { default as Menu } from './foundations/Menu';

// 组合模式 (Patterns) - 待实现
// export { default as ChatMessage } from './patterns/ChatMessage';
// export { default as SearchBar } from './patterns/SearchBar';
// export { default as HistoryItem } from './patterns/HistoryItem';

// 布局组件 (Layouts) - 待实现  
// export { default as SidePanel } from './layouts/SidePanel';
// export { default as ChatPanel } from './layouts/ChatPanel';
// export { default as AppLayout } from './layouts/AppLayout';

// 工具函数
export const classNames = (...classes) => {
  return classes.filter(Boolean).join(' ');
};

// 主题配置
export const theme = {
  colors: colorsToken,
  spacing: spacingToken,
  shadows: shadowsToken
};

// 版本信息
export const version = '1.0.0';
