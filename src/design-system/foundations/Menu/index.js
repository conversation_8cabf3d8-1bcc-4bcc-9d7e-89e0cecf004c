import React from 'react';

/**
 * Menu Component
 * 菜单组件 - 用于显示导航菜单项
 */

// Menu Item Component
const MenuItem = ({ 
  icon, 
  children, 
  active = false, 
  badge, 
  badgeColor = 'blue',
  onClick,
  className = '' 
}) => {
  const baseClasses = "w-full flex items-center gap-3 px-3 py-2 text-sm transition-colors rounded-lg";
  const activeClasses = active 
    ? "bg-gray-100 text-gray-900" 
    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900";
  
  const badgeColors = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    red: 'bg-red-100 text-red-600',
    yellow: 'bg-yellow-100 text-yellow-600',
    purple: 'bg-purple-100 text-purple-600'
  };

  return (
    <button 
      className={`${baseClasses} ${activeClasses} ${className}`}
      onClick={onClick}
    >
      {/* Icon */}
      {icon && (
        <span className="w-5 h-5 flex items-center justify-center">
          {icon}
        </span>
      )}
      
      {/* Text */}
      <span className="flex-1 text-left">{children}</span>
      
      {/* Badge */}
      {badge && (
        <span className={`text-xs px-2 py-1 rounded-full ${badgeColors[badgeColor]}`}>
          {badge}
        </span>
      )}
    </button>
  );
};

// Menu Group Component  
const MenuGroup = ({ title, children, className = '' }) => {
  return (
    <div className={className}>
      {title && (
        <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
          {title}
        </div>
      )}
      <div className="space-y-1">
        {children}
      </div>
    </div>
  );
};

// Main Menu Component
const Menu = ({ children, className = '' }) => {
  return (
    <nav className={`${className}`}>
      {children}
    </nav>
  );
};

// Attach sub-components
Menu.Group = MenuGroup;
Menu.Item = MenuItem;

export default Menu;
