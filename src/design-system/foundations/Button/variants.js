/**
 * Button 组件变体配置
 * 基于 AI Chat 界面的按钮样式分析
 */

export const buttonVariants = {
  // 主要按钮 - 深紫色主题（渐变色）
  primary: [
    'bg-gradient-to-b from-purple-600 to-purple-700 text-white',
    'hover:from-purple-700 hover:to-purple-800 hover:shadow-lg',
    'active:from-purple-800 active:to-purple-900',
    'focus:ring-purple-600/20',
    'disabled:from-gray-300 disabled:to-gray-400 disabled:text-gray-500'
  ].join(' '),

  // 次要按钮 - 灰色主题（渐变色）
  secondary: [
    'bg-gradient-to-b from-gray-100 to-gray-200 text-gray-700 border border-gray-200',
    'hover:from-gray-200 hover:to-gray-300 hover:border-gray-300 hover:shadow-lg',
    'active:from-gray-300 active:to-gray-400',
    'focus:ring-gray-500/20',
    'disabled:from-gray-50 disabled:to-gray-100 disabled:text-gray-400 disabled:border-gray-100'
  ].join(' '),

  // 幽灵按钮 - 透明背景
  ghost: [
    'bg-transparent text-gray-600',
    'hover:bg-gray-100 hover:text-gray-700',
    'active:bg-gray-200',
    'focus:ring-gray-500/20',
    'disabled:text-gray-400'
  ].join(' '),

  // 危险按钮 - 红色主题（渐变色）
  danger: [
    'bg-gradient-to-b from-red-500 to-red-600 text-white',
    'hover:from-red-600 hover:to-red-700 hover:shadow-lg',
    'active:from-red-700 active:to-red-800',
    'focus:ring-red-500/20',
    'disabled:from-gray-300 disabled:to-gray-400 disabled:text-gray-500'
  ].join(' '),

  // 成功按钮 - 绿色主题（渐变色）
  success: [
    'bg-gradient-to-b from-green-500 to-green-600 text-white',
    'hover:from-green-600 hover:to-green-700 hover:shadow-lg', 
    'active:from-green-700 active:to-green-800',
    'focus:ring-green-500/20',
    'disabled:from-gray-300 disabled:to-gray-400 disabled:text-gray-500'
  ].join(' '),

  // 轮廓按钮 - 边框样式（渐变悬停）
  outline: [
    'bg-transparent border-2 border-purple-600 text-purple-600',
    'hover:bg-gradient-to-b hover:from-purple-50 hover:to-purple-100 hover:border-purple-700 hover:text-purple-700 hover:shadow-lg',
    'active:from-purple-100 active:to-purple-200',
    'focus:ring-purple-600/20',
    'disabled:border-gray-300 disabled:text-gray-400'
  ].join(' ')
};

export const buttonSizes = {
  // 小尺寸 - 进一步增加高度
  sm: [
    'px-3 py-3',
    'text-sm font-semibold',
    'gap-1.5'
  ].join(' '),

  // 中等尺寸 (默认) - 进一步增加高度
  md: [
    'px-4 py-4',
    'text-base font-semibold',
    'gap-2'
  ].join(' '),

  // 大尺寸 - 进一步增加高度
  lg: [
    'px-6 py-5',
    'text-lg font-semibold',
    'gap-2'
  ].join(' '),

  // 超大尺寸 - 进一步增加高度
  xl: [
    'px-8 py-6',
    'text-xl font-semibold',
    'gap-3'
  ].join(' '),

  // 图标按钮
  icon: [
    'p-2',
    'text-sm'
  ].join(' ')
};

// 按钮用途配置
export const buttonPurpose = {
  // 发送消息按钮
  send: {
    variant: 'primary',
    size: 'md',
    icon: 'send'
  },

  // 搜索按钮
  search: {
    variant: 'ghost', 
    size: 'icon',
    icon: 'search'
  },

  // 历史记录按钮
  history: {
    variant: 'ghost',
    size: 'sm'
  },

  // 工具按钮
  tool: {
    variant: 'secondary',
    size: 'sm'
  },

  // 新建聊天按钮
  newChat: {
    variant: 'primary',
    size: 'md',
    icon: 'plus'
  }
};
