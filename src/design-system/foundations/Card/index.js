import React from 'react';

/**
 * Card 组件 - 基础卡片容器
 * 
 * @param {Object} props - 组件属性
 * @param {React.ReactNode} props.children - 卡片内容
 * @param {'sm'|'md'|'lg'} props.padding - 内边距尺寸
 * @param {'none'|'sm'|'md'|'lg'} props.shadow - 阴影级别
 * @param {boolean} props.hoverable - 是否可悬停
 * @param {boolean} props.bordered - 是否显示边框
 * @param {string} props.className - 自定义样式类
 * @param {Function} props.onClick - 点击事件
 */
const Card = ({
  children,
  padding = 'md',
  shadow = 'sm',
  hoverable = false,
  bordered = true,
  className = '',
  onClick,
  ...props
}) => {
  const baseClasses = 'bg-white rounded-lg transition-all duration-200';
  
  // 内边距配置
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6', 
    lg: 'p-8'
  };

  // 阴影配置
  const shadowClasses = {
    none: '',
    sm: 'shadow-card',
    md: 'shadow-md',
    lg: 'shadow-lg'
  };

  // 边框配置
  const borderClass = bordered ? 'border border-gray-200' : '';

  // 悬停效果
  const hoverClass = hoverable ? 'hover:shadow-hover hover:-translate-y-0.5 cursor-pointer' : '';

  // 点击效果
  const clickableClass = onClick ? 'active:scale-[0.99]' : '';

  const classes = [
    baseClasses,
    paddingClasses[padding] || paddingClasses.md,
    shadowClasses[shadow] || shadowClasses.sm,
    borderClass,
    hoverClass,
    clickableClass,
    className
  ].filter(Boolean).join(' ');

  return (
    <div
      className={classes}
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  );
};

// 预定义的卡片变体
export const ChatCard = ({ children, ...props }) => (
  <Card padding="md" shadow="sm" hoverable={false} {...props}>
    {children}
  </Card>
);

export const HistoryCard = ({ children, ...props }) => (
  <Card padding="sm" shadow="none" hoverable={true} bordered={false} {...props}>
    {children}
  </Card>
);

export const ToolCard = ({ children, ...props }) => (
  <Card padding="sm" shadow="sm" hoverable={true} {...props}>
    {children}
  </Card>
);

export default Card;
