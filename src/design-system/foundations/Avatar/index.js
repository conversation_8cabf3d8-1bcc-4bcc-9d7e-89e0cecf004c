import React from 'react';

/**
 * Avatar 组件 - 用户头像组件
 * 
 * @param {Object} props - 组件属性
 * @param {string} props.src - 头像图片地址
 * @param {string} props.alt - 图片描述
 * @param {string} props.name - 用户名 (用于生成初始字母)
 * @param {'xs'|'sm'|'md'|'lg'|'xl'} props.size - 头像尺寸
 * @param {'circle'|'rounded'|'square'} props.shape - 头像形状
 * @param {string} props.className - 自定义样式类
 * @param {boolean} props.online - 是否在线 (显示状态点)
 */
const Avatar = ({
  src,
  alt,
  name = '',
  size = 'md',
  shape = 'circle',
  className = '',
  online = false,
  ...props
}) => {
  // 尺寸配置
  const sizeClasses = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
    xl: 'w-16 h-16 text-xl'
  };

  // 形状配置
  const shapeClasses = {
    circle: 'rounded-full',
    rounded: 'rounded-lg',
    square: 'rounded-none'
  };

  // 在线状态点尺寸
  const statusSizes = {
    xs: 'w-1.5 h-1.5',
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5',
    lg: 'w-3 h-3',
    xl: 'w-4 h-4'
  };

  const baseClasses = 'relative inline-flex items-center justify-center bg-gray-100 text-gray-600 font-medium overflow-hidden';
  const sizeClass = sizeClasses[size] || sizeClasses.md;
  const shapeClass = shapeClasses[shape] || shapeClasses.circle;

  const classes = `${baseClasses} ${sizeClass} ${shapeClass} ${className}`.trim();

  // 生成初始字母
  const getInitials = (name) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={classes} {...props}>
      {src ? (
        <img
          src={src}
          alt={alt || name}
          className="w-full h-full object-cover"
          onError={(e) => {
            // 图片加载失败时显示初始字母
            e.target.style.display = 'none';
            e.target.nextSibling.style.display = 'flex';
          }}
        />
      ) : null}
      
      {/* 初始字母后备显示 */}
      <div 
        className={`w-full h-full flex items-center justify-center ${src ? 'hidden' : ''}`}
        style={{ display: src ? 'none' : 'flex' }}
      >
        {getInitials(name)}
      </div>

      {/* 在线状态指示器 */}
      {online && (
        <div className="absolute -bottom-0 -right-0 border-2 border-white rounded-full">
          <div className={`${statusSizes[size]} bg-green-500 rounded-full`} />
        </div>
      )}
    </div>
  );
};

// 预定义的头像变体
export const UserAvatar = ({ ...props }) => (
  <Avatar shape="circle" {...props} />
);

export const AIAvatar = ({ ...props }) => (
  <Avatar 
    shape="rounded" 
    src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233B82F6'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E"
    name="AI"
    {...props} 
  />
);

export default Avatar;
