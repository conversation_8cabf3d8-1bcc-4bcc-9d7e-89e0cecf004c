import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Avatar, Menu } from './design-system';
import { LanguageProvider, useLanguage } from './LanguageContext';
import LanguageSwitcher from './components/LanguageSwitcher';
import { formatRelativeTimeWithTranslation } from './utils/timeUtils';

// Custom hook for localStorage state
function useLocalStorageState(key, defaultValue) {
  const [state, setState] = useState(() => {
    try {
      const saved = localStorage.getItem(key);
      return saved ? JSON.parse(saved) : defaultValue;
    } catch {
      return defaultValue;
    }
  });

  useEffect(() => {
    localStorage.setItem(key, JSON.stringify(state));
  }, [key, state]);

  return [state, setState];
}

/**
 * Fiction Generator - Three-column layout
 * Left 20% - Sidebar, Center 50% - Chat, Right 30% - Output Panel
 */

// Internal component using translation system
function FictionGeneratorApp() {
  const { t, tArray } = useLanguage();
  const [activeTab, setActiveTab] = useLocalStorageState('fiction-generator-active-tab', 'long-novel');

  // Fiction type options
  const fictionTypes = tArray('fictionTypes');

  // Form state management
  const [formData, setFormData] = useState({
    storyAbout: "",
    targetAudience: t('audienceOptions.general'),
    fictionType: fictionTypes[0] || t('placeholders.defaultStoryType'),
    chapterCount: 50,
    writingElements: [],
    tone: "",
    // 主角设置
    protagonistCount: 1,
    protagonists: [
      { name: "", gender: t('form.genderOptions.male') }
    ],
    // 其他角色数量
    bossCount: 1,
    suitorCount: 0,
    villainCount: 0,
    helperCount: 0,
    // 时间维度
    timeDimension: t('form.timeUnits.year'),
    // 修仙设置
    isCultivation: false,
    cultivationSystem: t('form.cultivationSystems.traditional')
  });

  const [isGenerating, setIsGenerating] = useState(false);

  // Chat messages state
  const [messages, setMessages] = useState([]);

  // Handle sending messages
  const handleSendMessage = () => {
    if (inputValue.trim() === '') return;

    const newMessage = {
      id: Date.now(),
      content: inputValue.trim(),
      timestamp: new Date(),
      sender: 'user'
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue('');
  };

  // Handle card clicks
  const handleCardClick = (cardType) => {
    const prompts = {
      writeCopy: t('chat.welcome.cards.writeCopy.title'),
      imageGeneration: t('chat.welcome.cards.imageGeneration.title'),
      createAvatar: t('chat.welcome.cards.createAvatar.title'),
      writeCode: t('chat.welcome.cards.writeCode.title')
    };

    setInputValue(prompts[cardType] || '');
  };

  // Dynamic placeholder state
  const [currentPlaceholderIndex, setCurrentPlaceholderIndex] = useState(0);
  const placeholderExamples = tArray('chat.input.placeholderExamples');

  // Dynamic help text state
  const [currentHelpTextIndex, setCurrentHelpTextIndex] = useState(0);
  const [helpTextFading, setHelpTextFading] = useState(false);
  const [placeholderFading, setPlaceholderFading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const helpTextExamples = tArray('chat.input.helpTextExamples');

  // Dynamic placeholder rotation effect with fade animation
  useEffect(() => {
    if (placeholderExamples.length > 0) {
      const interval = setInterval(() => {
        setPlaceholderFading(true);
        setTimeout(() => {
          setCurrentPlaceholderIndex((prevIndex) =>
            (prevIndex + 1) % placeholderExamples.length
          );
          setTimeout(() => {
            setPlaceholderFading(false);
          }, 30); // Small delay before fade in
        }, 250); // 250ms fade out, then change text, then fade in
      }, 5000); // Change every 5 seconds

      return () => clearInterval(interval);
    }
  }, [placeholderExamples.length]);

  // Dynamic help text rotation effect with fade animation
  useEffect(() => {
    if (helpTextExamples.length > 0) {
      const interval = setInterval(() => {
        setHelpTextFading(true);
        setTimeout(() => {
          setCurrentHelpTextIndex((prevIndex) =>
            (prevIndex + 1) % helpTextExamples.length
          );
          setTimeout(() => {
            setHelpTextFading(false);
          }, 50); // Small delay before fade in
        }, 300); // 300ms fade out, then change text, then fade in
      }, 6000); // Change every 6 seconds (different from placeholder)

      return () => clearInterval(interval);
    }
  }, [helpTextExamples.length]);

  // 用于演示时间格式的状态
  const [lastTestTime] = useState(new Date(Date.now() - 60 * 1000)); // 1分钟前



  // Writing elements options
  const writingElements = tArray('writingElements');

  // Clear all inputs
  const clearAllInputs = () => {
    setFormData({
      storyAbout: "",
      targetAudience: t('audienceOptions.general'),
      fictionType: fictionTypes[0] || t('placeholders.defaultStoryType'),
      chapterCount: 50,
      writingElements: [],
      tone: "",
      // 主角设置
      protagonistCount: 1,
      protagonists: [
        { name: "", gender: t('form.genderOptions.male') }
      ],
      // 其他角色数量
      bossCount: 1,
      suitorCount: 0,
      villainCount: 0,
      helperCount: 0,
      // 时间维度
      timeDimension: t('form.timeUnits.year'),
      // 修仙设置
      isCultivation: false,
      cultivationSystem: t('form.cultivationSystems.traditional')
    });
  };

  // Generate content
  const handleGenerate = async () => {
    if (!formData.storyAbout.trim()) {
      alert(t('alerts.enterStoryContent'));
      return;
    }

    setIsGenerating(true);

    // Mock API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      // Here you can integrate actual AI generation API
      console.log('Generating content with:', formData);
      alert(t('alerts.generationSuccess'));
    } catch (error) {
      console.error('Generation failed:', error);
      alert(t('alerts.generationFailed'));
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle protagonist count change
  const handleProtagonistCountChange = (count) => {
    const newCount = Math.max(1, Math.min(10, count));
    setFormData(prev => {
      const newProtagonists = [...prev.protagonists];

      // Add new protagonists if count increased
      while (newProtagonists.length < newCount) {
        newProtagonists.push({ name: "", gender: t('form.genderOptions.male') });
      }

      // Remove protagonists if count decreased
      while (newProtagonists.length > newCount) {
        newProtagonists.pop();
      }

      return {
        ...prev,
        protagonistCount: newCount,
        protagonists: newProtagonists
      };
    });
  };

  // Handle protagonist property change
  const handleProtagonistChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      protagonists: prev.protagonists.map((protagonist, i) =>
        i === index ? { ...protagonist, [field]: value } : protagonist
      )
    }));
  };

  return (
    <div className="h-screen bg-gray-50 flex overflow-hidden min-w-[1200px]">

      {/* Left sidebar - 20% */}
      <div className="w-1/5 min-w-[350px] bg-white border-r border-gray-200 flex flex-col">

        {/* Logo area */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-black rounded-lg flex items-center justify-center">
              <span className="text-2xl">💬</span>
            </div>
            <span className="text-4xl font-semibold text-gray-900">Fictionist</span>
          </div>
        </div>

        {/* Create Fiction button */}
        <div className="p-4 border-b border-gray-100">
          <Button variant="primary" className="w-full">
            + Create Content
          </Button>
        </div>

        {/* Menu area */}
        <div className="flex-1 p-4">
          <Menu>
            <Menu.Group title={t('menu.title')}>
              <Menu.Item
                icon="📊"
                onClick={() => console.log('Dashboard clicked')}
              >
                {t('menu.items.dashboard')}
              </Menu.Item>

              <Menu.Item
                icon="📝"
                onClick={() => console.log('Templates clicked')}
              >
                {t('menu.items.templates')}
              </Menu.Item>

              <Menu.Item
                icon="💬"
                active={true}
                badge={t('menu.badges.beta')}
                badgeColor="blue"
                onClick={() => console.log('Chat clicked')}
              >
                {t('menu.items.chat')}
              </Menu.Item>

              <Menu.Item
                icon="📄"
                onClick={() => console.log('Documents clicked')}
              >
                {t('menu.items.documents')}
              </Menu.Item>

              <Menu.Item
                icon="🎨"
                onClick={() => console.log('Art clicked')}
              >
                {t('menu.items.art')}
              </Menu.Item>

              <Menu.Item
                icon="⚙️"
                onClick={() => console.log('Settings clicked')}
              >
                {t('menu.items.settings')}
              </Menu.Item>

              <Menu.Item
                icon="❓"
                onClick={() => console.log('Help clicked')}
              >
                {t('menu.items.help')}
              </Menu.Item>
            </Menu.Group>
          </Menu>
                </div>

        {/* Bottom fixed area */}
        <div className="border-t border-gray-100">
          {/* Trial reminder */}
          <div className="pt-12 px-4 pb-4 bg-gradient-to-br from-yellow-50 to-orange-50 border border-yellow-200 m-4 rounded-lg">
            <div className="text-center mb-3 relative overflow-visible">
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 z-0 w-20 h-6">
                <div className="text-sm absolute top-1 left-1 transform -rotate-12">⭐</div>
                <div className="text-sm absolute top-0 left-1/2 transform -translate-x-1/2">⭐</div>
                <div className="text-sm absolute top-1 right-1 transform rotate-12">⭐</div>
                </div>
              <div className="w-16 h-16 mx-auto mb-2 flex items-center justify-center overflow-visible relative">
                <span className="text-7xl leading-none">👍</span>
              </div>
              <div className="text-sm font-medium text-gray-900 mb-1">{t('trial.endsIn', { days: 4 })}</div>
              <div className="text-xs text-gray-600">{t('trial.planDescription')}</div>
            </div>
            <Button variant="secondary" size="sm" className="w-full text-xs">
              {t('buttons.viewDetails')}
            </Button>
          </div>

          {/* User info */}
          <div className="p-4">
            <div className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-3">
                <Avatar name="Drian moreno" size="sm" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900">Drian moreno</div>
                  <div className="text-xs text-gray-500 truncate"><EMAIL></div>
                  {/* 最后测试时间显示 */}
                  <div className="text-xs text-gray-400 mt-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span>{t('time.lastTested')}: {formatRelativeTimeWithTranslation(lastTestTime, t)}</span>
                    </div>
                </div>
                </div>
                <button className="text-gray-400 hover:text-gray-600">
                  ▼
                </button>
              </div>
                </div>
                </div>
                </div>
              </div>

      {/* Right main area - contains header + content */}
      <div className="flex-1 flex flex-col bg-white">

        {/* Unified header across the entire right area */}
        <div className="p-5 border-b border-gray-100 border-l border-gray-200 bg-white flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-2xl">🗒️</span>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">{t('appTitle')}</h1>
              <p className="text-sm text-gray-600">
                {t('content.appDescription')}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            {/* Upgrade Button */}
            <button className="bg-gradient-to-r from-black to-gray-800 hover:from-gray-800 hover:to-black text-white font-semibold px-6 py-3 rounded-xl text-base flex items-center gap-2 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <span className="text-lg">⚡</span>
              {t('buttons.upgrade')}
            </button>

            {/* Help Button */}
            <button className="bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 text-blue-700 w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-md hover:shadow-lg group">
              <span className="text-lg font-medium group-hover:rotate-12 transition-transform">❓</span>
            </button>

            {/* Gift Button */}
            <button className="bg-gradient-to-r from-purple-50 to-pink-50 hover:from-purple-100 hover:to-pink-100 text-purple-700 w-12 h-12 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-md hover:shadow-lg group">
              <span className="text-lg group-hover:bounce-animation">🎁</span>
            </button>

            <LanguageSwitcher className="flex items-center gap-2" />
                  </div>
                </div>

        {/* Content area - split into chat (left) and properties (right) */}
        <div className="flex-1 flex h-0">

          {/* Chat window - left side */}
          <div className="flex-1 min-w-[400px] border-l border-gray-200 flex flex-col bg-white">
            {/* Chat content */}
            <div className="flex-1 overflow-y-auto">
              {messages.length === 0 ? (
                // Welcome screen when no messages
                <div className="h-full flex flex-col items-center justify-center p-12">
                  <div className="max-w-4xl w-full text-center">
                    {/* Welcome title */}
                    <h1 className="text-6xl font-bold text-gray-900 mb-6">
                      {t('chat.welcome.title')}
                    </h1>

                    {/* Welcome subtitle */}
                    <p className="text-xl text-gray-600 mb-12 leading-relaxed">
                      {t('chat.welcome.subtitle')}
                    </p>

                    {/* Feature cards */}
                    <div className="grid grid-cols-2 gap-6 max-w-2xl mx-auto">
                      <button
                        className="flex items-center justify-between p-6 bg-orange-50 border border-orange-200 rounded-xl hover:bg-orange-100 transition-colors text-left group"
                        onClick={() => handleCardClick('findInspiration')}
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center text-xl">
                            💡
                          </div>
                          <span className="text-gray-900 font-medium">
                            {t('chat.welcome.cards.findInspiration.title')}
                          </span>
                        </div>
                        <div className="text-gray-400 group-hover:text-gray-600 transition-colors">
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M6 1V11M1 6H11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                          </svg>
                        </div>
                      </button>

                      <button
                        className="flex items-center justify-between p-6 bg-blue-50 border border-blue-200 rounded-xl hover:bg-blue-100 transition-colors text-left group"
                        onClick={() => handleCardClick('characterGeneration')}
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center text-xl">
                            🎭
                          </div>
                          <span className="text-gray-900 font-medium">
                            {t('chat.welcome.cards.characterGeneration.title')}
                          </span>
                        </div>
                        <div className="text-gray-400 group-hover:text-gray-600 transition-colors">
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M6 1V11M1 6H11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                          </svg>
                        </div>
                      </button>

                      <button
                        className="flex items-center justify-between p-6 bg-green-50 border border-green-200 rounded-xl hover:bg-green-100 transition-colors text-left group"
                        onClick={() => handleCardClick('storyIntroduction')}
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center text-xl">
                            📖
                          </div>
                          <span className="text-gray-900 font-medium">
                            {t('chat.welcome.cards.storyIntroduction.title')}
                          </span>
                        </div>
                        <div className="text-gray-400 group-hover:text-gray-600 transition-colors">
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M6 1V11M1 6H11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                          </svg>
                        </div>
                      </button>

                      <button
                        className="flex items-center justify-between p-6 bg-pink-50 border border-pink-200 rounded-xl hover:bg-pink-100 transition-colors text-left group"
                        onClick={() => handleCardClick('aiPolish')}
                      >
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center text-xl">
                            ✨
                          </div>
                          <span className="text-gray-900 font-medium">
                            {t('chat.welcome.cards.aiPolish.title')}
                          </span>
                        </div>
                        <div className="text-gray-400 group-hover:text-gray-600 transition-colors">
                          <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M6 1V11M1 6H11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                          </svg>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                // Chat messages when there are messages
                <div className="p-4 space-y-4">
                  {messages.map((message, index) => (
                    <Card key={index} padding="md" className="border border-gray-200">
                      <div className="mb-3">
                        <p className="text-sm text-gray-900 leading-relaxed mb-2">
                          {message.content}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            {formatRelativeTimeWithTranslation(message.timestamp, t)}
                          </span>
                          <div className="flex gap-1">
                            <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                              <span className="text-sm">📄</span>
                            </button>
                            <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                              <span className="text-sm">👍</span>
                            </button>
                            <button className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                              <span className="text-sm">👎</span>
                            </button>
                          </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                </div>
              )}
            </div>

            {/* Chat input area - absolute bottom */}
            <div className="bg-white p-4">
              {/* Main input container */}
              <div className="relative border border-gray-300 rounded-2xl bg-white">
                {/* Custom placeholder with fade effect */}
                {inputValue === '' && (
                  <div className={`absolute left-4 top-4 pointer-events-none text-gray-500 transition-opacity duration-250 ease-in-out ${placeholderFading ? 'opacity-0' : 'opacity-100'}`}>
                    {placeholderExamples[currentPlaceholderIndex] || t('chat.input.placeholder')}
                  </div>
                )}
                {/* Input field */}
                <textarea
                  className="w-full px-4 py-4 pr-12 resize-none overflow-hidden bg-transparent border-none outline-none text-gray-900"
                  placeholder=""
                  value={inputValue}
                  rows="1"
                  style={{ minHeight: '56px', maxHeight: '120px' }}
                  onChange={(e) => {
                    setInputValue(e.target.value);
                  }}
                  onInput={(e) => {
                    // Auto-resize textarea
                    e.target.style.height = 'auto';
                    e.target.style.height = Math.min(e.target.scrollHeight, 120) + 'px';
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                />

                {/* Send button - positioned in top right */}
                <button
                  className="absolute top-2 right-2 p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  title={t('chat.input.send')}
                  onClick={handleSendMessage}
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <line x1="22" y1="2" x2="11" y2="13" />
                    <polygon points="22,2 15,22 11,13 2,9 22,2" />
                  </svg>
                </button>

                {/* Bottom section */}
                <div className="flex items-center justify-between px-4 pb-3">
                  {/* Left side - Action buttons */}
                  <div className="flex items-center gap-4">
                    {/* Attach button */}
                    <button
                      className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                      title={t('chat.input.attach')}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48" />
                      </svg>
                      {t('chat.input.attach')}
                    </button>

                    {/* Voice message button */}
                    <button
                      className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                      title={t('chat.input.voiceMessage')}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" />
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
                        <line x1="12" y1="19" x2="12" y2="23" />
                        <line x1="8" y1="23" x2="16" y2="23" />
                      </svg>
                      {t('chat.input.voiceMessage')}
                    </button>

                    {/* Browse prompts button */}
                    <button
                      className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                      title={t('chat.input.browsePrompts')}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <circle cx="11" cy="11" r="8" />
                        <path d="m21 21-4.35-4.35" />
                      </svg>
                      {t('chat.input.browsePrompts')}
                    </button>
                  </div>

                  {/* Right side - Character count */}
                  <div className="text-sm text-gray-400">
                    20 / 3,000
                  </div>
                </div>
              </div>

              {/* Help text */}
              <div className={`mt-2 text-xs text-gray-500 text-center transition-opacity duration-300 ease-in-out ${helpTextFading ? 'opacity-0' : 'opacity-100'}`}>
                {helpTextExamples[currentHelpTextIndex] || t('chat.input.helpText')}
              </div>
            </div>
          </div>

          {/* Properties panel - right side */}
          <div className="w-1/3 min-w-[400px] bg-white border-l border-gray-200 flex flex-col">
            {/* Properties header */}
            <div className="p-4 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">{t('properties.title')}</h3>
            </div>

            {/* Properties content */}
            <div className="flex-1 p-6 overflow-y-auto">

              {/* Tab switching */}
              <div className="mb-6">
                <div className="flex gap-4 mb-4">
                  <button
                    className={`flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${activeTab === 'long-novel'
                        ? 'text-purple-600 bg-purple-50 border border-purple-200'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                      }`}
                    onClick={() => setActiveTab('long-novel')}
                  >
                    {t('novelTypes.longNovel')}
                  </button>
                  <button
                    className={`flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 ${activeTab === 'short-novel'
                        ? 'text-purple-600 bg-purple-50 border border-purple-200'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                      }`}
                    onClick={() => setActiveTab('short-novel')}
                  >
                    {t('novelTypes.shortNovel')}
                  </button>
                </div>

                {/* Type descriptions */}
                <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-600 mb-4">
                  {activeTab === 'long-novel' ? (
                    <div>
                      <div className="font-medium text-gray-800 mb-2">{t('novelTypes.descriptions.longNovel.title')}</div>
                      <ul className="space-y-1">
                        {tArray('novelTypes.descriptions.longNovel.features').map((feature, index) => (
                          <li key={index}>{feature}</li>
                        ))}
                      </ul>
                          </div>
                  ) : (
                    <div>
                      <div className="font-medium text-gray-800 mb-2">{t('novelTypes.descriptions.shortNovel.title')}</div>
                      <ul className="space-y-1">
                        {tArray('novelTypes.descriptions.shortNovel.features').map((feature, index) => (
                          <li key={index}>{feature}</li>
                        ))}
                      </ul>
                            </div>
                          )}
                        </div>
                      </div>

              <div className="space-y-5">

                {/* 📝 基础信息卡片 */}
                <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-lg">📝</span>
                    <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.basicInfo')}</h3>
                  </div>

                  {/* Fiction story content */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('form.storyAbout')}
                      <span className="ml-2 text-xs text-gray-500">{formData.storyAbout.length}/400</span>
                    </label>
                    <div className="relative">
                      <textarea
                        className="w-full h-32 px-4 py-3 pb-12 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm transition-colors"
                        placeholder={t('form.storyPlaceholder')}
                        value={formData.storyAbout}
                        onChange={(e) => handleInputChange('storyAbout', e.target.value)}
                        maxLength={400}
                      />
                      <button
                        className="absolute bottom-3 right-3 px-3 py-1.5 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg bg-gray-50 border border-gray-200 transition-colors"
                        onClick={() => handleInputChange('storyAbout', '')}
                      >
                        {t('buttons.clear')}
                      </button>
                    </div>
                  </div>

                  {/* Target audience */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('form.targetAudience')}
                    </label>
                    <div className="flex gap-3">
                      {[
                        { key: 'male', value: t('audienceOptions.male') },
                        { key: 'female', value: t('audienceOptions.female') },
                        { key: 'general', value: t('audienceOptions.general') }
                      ].map(({ key, value }) => (
                        <button
                          key={key}
                          className={`flex-1 px-4 py-3 rounded-xl border text-sm font-medium transition-all duration-200 ${formData.targetAudience === value
                              ? 'bg-purple-600 text-white border-purple-600 shadow-sm'
                              : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:text-purple-600 hover:shadow-sm'
                            }`}
                          onClick={() => handleInputChange('targetAudience', value)}
                        >
                          {value}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Chapter count */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('form.chapterCount')}
                    </label>
                    <div className="flex items-center border border-gray-300 rounded-xl overflow-hidden focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-purple-500 transition-colors">
                      <button
                        type="button"
                        className="px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-r border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={() => handleInputChange('chapterCount', Math.max(1, formData.chapterCount - 1))}
                        disabled={formData.chapterCount <= 1}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </button>
                      <input
                        type="number"
                        className="flex-1 px-4 py-3 text-center border-0 focus:outline-none text-sm bg-white"
                        value={formData.chapterCount}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 1;
                          if (value >= 1 && value <= 1000) {
                            handleInputChange('chapterCount', value);
                          }
                        }}
                        min="1"
                        max="1000"
                      />
                      <button
                        type="button"
                        className="px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-l border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={() => handleInputChange('chapterCount', Math.min(1000, formData.chapterCount + 1))}
                        disabled={formData.chapterCount >= 1000}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">{t('form.chapterRange')}</p>
                  </div>
                </div>

                {/* 🎭 类型设置卡片 */}
                <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-lg">🎭</span>
                    <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.typeSettings')}</h3>
                  </div>

                  <div className="grid grid-cols-1 gap-4">
                    {/* Fiction type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        {t('form.fictionType')}
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {fictionTypes.map((type) => (
                          <button
                            key={type}
                            className={`px-3 py-2.5 text-sm rounded-lg border transition-all duration-200 font-medium ${formData.fictionType === type
                                ? 'bg-purple-500 text-white border-purple-500 shadow-md'
                                : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:text-purple-600 hover:shadow-sm'
                              }`}
                            onClick={() => handleInputChange('fictionType', type)}
                          >
                            {type}
                          </button>
                        ))}
                      </div>
                    </div>

                  </div>
                </div>

                {/* 👤 主角设置卡片 */}
                <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-lg">👤</span>
                    <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.protagonistSettings')}</h3>
                  </div>

                  {/* 主角个数设置 */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('form.protagonistCount')}
                    </label>
                    <div className="flex items-center border border-gray-300 rounded-xl overflow-hidden focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-purple-500 transition-colors">
                      <button
                        type="button"
                        className="px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-r border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={() => handleProtagonistCountChange(formData.protagonistCount - 1)}
                        disabled={formData.protagonistCount <= 1}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </button>
                      <input
                        type="number"
                        className="flex-1 px-4 py-3 text-center border-0 focus:outline-none text-sm bg-white"
                        value={formData.protagonistCount}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 1;
                          if (value >= 1 && value <= 10) {
                            handleProtagonistCountChange(value);
                          }
                        }}
                        min="1"
                        max="10"
                      />
                      <button
                        type="button"
                        className="px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-l border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={() => handleProtagonistCountChange(formData.protagonistCount + 1)}
                        disabled={formData.protagonistCount >= 10}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 mt-2">{t('form.protagonistRange')}</p>
                  </div>

                  {/* 主角详细信息 */}
                  <div className="space-y-4">
                    {formData.protagonists.map((protagonist, index) => (
                      <div key={index} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <h4 className="text-sm font-medium text-gray-700 mb-3">{t('form.protagonistNumber', { number: index + 1 })}</h4>
                        <div className="grid grid-cols-2 gap-3">
                          {/* 姓名 */}
                          <div>
                            <label className="block text-xs text-gray-600 mb-2">{t('form.name')}</label>
                            <input
                              type="text"
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                              placeholder={t('form.namePlaceholder')}
                              value={protagonist.name}
                              onChange={(e) => handleProtagonistChange(index, 'name', e.target.value)}
                            />
                          </div>
                          {/* 性别 */}
                          <div>
                            <label className="block text-xs text-gray-600 mb-2">{t('form.gender')}</label>
                            <select
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                              value={protagonist.gender}
                              onChange={(e) => handleProtagonistChange(index, 'gender', e.target.value)}
                            >
                              <option value={t('form.genderOptions.male')}>{t('form.genderOptions.male')}</option>
                              <option value={t('form.genderOptions.female')}>{t('form.genderOptions.female')}</option>
                              <option value={t('form.genderOptions.other')}>{t('form.genderOptions.other')}</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 🎭 其他角色设置卡片 */}
                <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-lg">🎭</span>
                    <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.otherCharacters')}</h3>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {/* BOSS */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        {t('form.bossCount')}
                      </label>
                      <div className="flex items-center border border-gray-300 rounded-xl overflow-hidden focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-purple-500 transition-colors">
                        <button
                          type="button"
                          className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-r border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleInputChange('bossCount', Math.max(0, formData.bossCount - 1))}
                          disabled={formData.bossCount <= 0}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                        <input
                          type="number"
                          className="flex-1 px-3 py-2 text-center border-0 focus:outline-none text-sm bg-white"
                          value={formData.bossCount}
                          onChange={(e) => {
                            const value = parseInt(e.target.value) || 0;
                            if (value >= 0 && value <= 20) {
                              handleInputChange('bossCount', value);
                            }
                          }}
                          min="0"
                          max="20"
                        />
                        <button
                          type="button"
                          className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-l border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleInputChange('bossCount', Math.min(20, formData.bossCount + 1))}
                          disabled={formData.bossCount >= 20}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* 追求者 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        {t('form.suitorCount')}
                      </label>
                      <div className="flex items-center border border-gray-300 rounded-xl overflow-hidden focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-purple-500 transition-colors">
                        <button
                          type="button"
                          className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-r border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleInputChange('suitorCount', Math.max(0, formData.suitorCount - 1))}
                          disabled={formData.suitorCount <= 0}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                        <input
                          type="number"
                          className="flex-1 px-3 py-2 text-center border-0 focus:outline-none text-sm bg-white"
                          value={formData.suitorCount}
                          onChange={(e) => {
                            const value = parseInt(e.target.value) || 0;
                            if (value >= 0 && value <= 20) {
                              handleInputChange('suitorCount', value);
                            }
                          }}
                          min="0"
                          max="20"
                        />
                        <button
                          type="button"
                          className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-l border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleInputChange('suitorCount', Math.min(20, formData.suitorCount + 1))}
                          disabled={formData.suitorCount >= 20}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* 反派 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        {t('form.villainCount')}
                      </label>
                      <div className="flex items-center border border-gray-300 rounded-xl overflow-hidden focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-purple-500 transition-colors">
                        <button
                          type="button"
                          className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-r border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleInputChange('villainCount', Math.max(0, formData.villainCount - 1))}
                          disabled={formData.villainCount <= 0}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                        <input
                          type="number"
                          className="flex-1 px-3 py-2 text-center border-0 focus:outline-none text-sm bg-white"
                          value={formData.villainCount}
                          onChange={(e) => {
                            const value = parseInt(e.target.value) || 0;
                            if (value >= 0 && value <= 20) {
                              handleInputChange('villainCount', value);
                            }
                          }}
                          min="0"
                          max="20"
                        />
                        <button
                          type="button"
                          className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-l border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleInputChange('villainCount', Math.min(20, formData.villainCount + 1))}
                          disabled={formData.villainCount >= 20}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* 帮手 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        {t('form.helperCount')}
                      </label>
                      <div className="flex items-center border border-gray-300 rounded-xl overflow-hidden focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-purple-500 transition-colors">
                        <button
                          type="button"
                          className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-r border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleInputChange('helperCount', Math.max(0, formData.helperCount - 1))}
                          disabled={formData.helperCount <= 0}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                        <input
                          type="number"
                          className="flex-1 px-3 py-2 text-center border-0 focus:outline-none text-sm bg-white"
                          value={formData.helperCount}
                          onChange={(e) => {
                            const value = parseInt(e.target.value) || 0;
                            if (value >= 0 && value <= 20) {
                              handleInputChange('helperCount', value);
                            }
                          }}
                          min="0"
                          max="20"
                        />
                        <button
                          type="button"
                          className="px-3 py-2 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors border-l border-gray-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleInputChange('helperCount', Math.min(20, formData.helperCount + 1))}
                          disabled={formData.helperCount >= 20}
                        >
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* ✨ 写作元素卡片 */}
                <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-lg">✨</span>
                    <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.writingElements')}</h3>
                  </div>

                  {/* Writing elements */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('form.writingElements')}
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      {writingElements.map((element) => (
                        <button
                          key={element}
                          className={`px-3 py-2.5 rounded-xl border text-xs font-medium transition-all duration-200 ${formData.writingElements.includes(element)
                              ? 'bg-purple-600 text-white border-purple-600 shadow-sm'
                              : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:text-purple-600 hover:shadow-sm'
                            }`}
                          onClick={() => {
                            const elements = formData.writingElements.includes(element)
                              ? formData.writingElements.filter(e => e !== element)
                              : [...formData.writingElements, element];
                            handleInputChange('writingElements', elements);
                          }}
                        >
                          {element}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Tone style */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('form.toneStyle')}
                    </label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm transition-colors"
                      placeholder={t('form.tonePlaceholder')}
                      value={formData.tone}
                      onChange={(e) => handleInputChange('tone', e.target.value)}
                    />
                  </div>
                </div>

                {/* ⏰ 时间维度设置卡片 */}
                <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-lg">⏰</span>
                    <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.timeSettings')}</h3>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('form.timeDimension')}
                    </label>
                    <div className="grid grid-cols-4 gap-2">
                      {[
                        { key: 'day', value: t('form.timeUnits.day') },
                        { key: 'month', value: t('form.timeUnits.month') },
                        { key: 'year', value: t('form.timeUnits.year') },
                        { key: 'decade', value: t('form.timeUnits.decade') },
                        { key: 'century', value: t('form.timeUnits.century') },
                        { key: 'millennium', value: t('form.timeUnits.millennium') },
                        { key: 'tenThousandYears', value: t('form.timeUnits.tenThousandYears') }
                      ].map((timeUnit) => (
                        <button
                          key={timeUnit.key}
                          className={`px-3 py-2.5 text-sm rounded-lg border transition-all duration-200 font-medium ${formData.timeDimension === timeUnit.value
                              ? 'bg-purple-500 text-white border-purple-500 shadow-md'
                              : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:text-purple-600 hover:shadow-sm'
                            }`}
                          onClick={() => handleInputChange('timeDimension', timeUnit.value)}
                        >
                          {timeUnit.value}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* 🧙‍♂️ 修仙设置卡片 */}
                <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-gray-300 hover:-translate-y-1">
                  <div className="flex items-center gap-2 mb-4">
                    <span className="text-lg">🧙‍♂️</span>
                    <h3 className="text-sm font-semibold text-gray-800">{t('form.cardTitles.cultivationSettings')}</h3>
                  </div>

                  {/* 是否修仙 */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      {t('form.cultivationQuestion')}
                    </label>
                    <div className="flex gap-3">
                      <button
                        className={`flex-1 px-4 py-3 rounded-xl border text-sm font-medium transition-all duration-200 ${formData.isCultivation
                            ? 'bg-purple-600 text-white border-purple-600 shadow-sm'
                            : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:text-purple-600 hover:shadow-sm'
                          }`}
                        onClick={() => handleInputChange('isCultivation', true)}
                      >
                        {t('form.yes')}
                      </button>
                      <button
                        className={`flex-1 px-4 py-3 rounded-xl border text-sm font-medium transition-all duration-200 ${!formData.isCultivation
                            ? 'bg-purple-600 text-white border-purple-600 shadow-sm'
                            : 'bg-white text-gray-700 border-gray-300 hover:border-purple-400 hover:text-purple-600 hover:shadow-sm'
                          }`}
                        onClick={() => handleInputChange('isCultivation', false)}
                      >
                        {t('form.no')}
                      </button>
                    </div>
                  </div>

                  {/* 修仙体系选择 */}
                  {formData.isCultivation && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        {t('form.cultivationSystem')}
                      </label>
                      <select
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
                        value={formData.cultivationSystem}
                        onChange={(e) => handleInputChange('cultivationSystem', e.target.value)}
                      >
                        <option value={t('form.cultivationSystems.traditional')}>{t('form.cultivationSystems.traditional')}</option>
                        <option value={t('form.cultivationSystems.essenceToQi')}>{t('form.cultivationSystems.essenceToQi')}</option>
                        <option value={t('form.cultivationSystems.wheelSeaPalace')}>{t('form.cultivationSystems.wheelSeaPalace')}</option>
                        <option value={t('form.cultivationSystems.bloodMoving')}>{t('form.cultivationSystems.bloodMoving')}</option>
                        <option value={t('form.cultivationSystems.foundationToImmortal')}>{t('form.cultivationSystems.foundationToImmortal')}</option>
                        <option value={t('form.cultivationSystems.godManSystem')}>{t('form.cultivationSystems.godManSystem')}</option>
                        <option value={t('form.cultivationSystems.simplifiedCultivation')}>{t('form.cultivationSystems.simplifiedCultivation')}</option>
                        <option value={t('form.cultivationSystems.bodyBuilding')}>{t('form.cultivationSystems.bodyBuilding')}</option>
                        <option value={t('form.cultivationSystems.physicalAdvancement')}>{t('form.cultivationSystems.physicalAdvancement')}</option>
                        <option value={t('form.cultivationSystems.cosmicWarrior')}>{t('form.cultivationSystems.cosmicWarrior')}</option>
                        <option value={t('form.cultivationSystems.awakeningDao')}>{t('form.cultivationSystems.awakeningDao')}</option>
                        <option value={t('form.cultivationSystems.sensingMaster')}>{t('form.cultivationSystems.sensingMaster')}</option>
                        <option value={t('form.cultivationSystems.openingChannels')}>{t('form.cultivationSystems.openingChannels')}</option>
                        <option value={t('form.cultivationSystems.condensingQi')}>{t('form.cultivationSystems.condensingQi')}</option>
                        <option value={t('form.cultivationSystems.temperingBody')}>{t('form.cultivationSystems.temperingBody')}</option>
                        <option value={t('form.cultivationSystems.talismanMaster')}>{t('form.cultivationSystems.talismanMaster')}</option>
                        <option value={t('form.cultivationSystems.scholarWay')}>{t('form.cultivationSystems.scholarWay')}</option>
                        <option value={t('form.cultivationSystems.fightingQi')}>{t('form.cultivationSystems.fightingQi')}</option>
                        <option value={t('form.cultivationSystems.soulMaster')}>{t('form.cultivationSystems.soulMaster')}</option>
                        <option value={t('form.cultivationSystems.supremeSystem')}>{t('form.cultivationSystems.supremeSystem')}</option>
                        <option value={t('form.cultivationSystems.martialSage')}>{t('form.cultivationSystems.martialSage')}</option>
                        <option value={t('form.cultivationSystems.ghostWay')}>{t('form.cultivationSystems.ghostWay')}</option>
                      </select>
                    </div>
                  )}
                </div>

                {/* Fixed bottom button area */}
                <div className="border-t border-gray-200 p-6 bg-white shadow-lg backdrop-blur-sm bg-white/95">
                  <div className="flex items-center justify-between">
                    <button
                      className="flex items-center gap-2 text-sm text-gray-500 hover:text-gray-700 transition-colors"
                      onClick={clearAllInputs}
                      disabled={isGenerating}
                    >
                      <span>✗</span>
                      {t('buttons.clearAll')}
                    </button>
                    <Button
                      variant="primary"
                      className="px-6"
                      onClick={handleGenerate}
                      disabled={isGenerating || !formData.storyAbout.trim()}
                    >
                      <span className="mr-2">✨</span>
                      {isGenerating ? t('buttons.generating') : t('buttons.generate')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main App component with LanguageProvider wrapper
export default function App() {
  return (
    <LanguageProvider>
      <FictionGeneratorApp />
    </LanguageProvider>
  );
}
